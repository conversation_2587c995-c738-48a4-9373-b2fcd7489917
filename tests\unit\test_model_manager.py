"""
Comprehensive unit tests for the enhanced model management system.
"""

import pytest
import numpy as np
import pandas as pd
import tempfile
import joblib
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
from datetime import datetime

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from core.model_manager import (
    ModelCache, ModelManager, ModelMetadata, model_manager
)
from core.exceptions import ModelError, ValidationError


class TestModelMetadata:
    """Test the ModelMetadata dataclass"""
    
    def test_model_metadata_creation(self):
        """Test creating ModelMetadata"""
        created_at = datetime.now()
        last_used = datetime.now()
        
        metadata = ModelMetadata(
            name="test_model",
            path="/path/to/model.pkl",
            model_type="RandomForestClassifier",
            version="1.0",
            created_at=created_at,
            last_used=last_used,
            accuracy=0.85,
            size_mb=10.5,
            feature_count=20
        )
        
        assert metadata.name == "test_model"
        assert metadata.path == "/path/to/model.pkl"
        assert metadata.model_type == "RandomForestClassifier"
        assert metadata.version == "1.0"
        assert metadata.created_at == created_at
        assert metadata.last_used == last_used
        assert metadata.accuracy == 0.85
        assert metadata.size_mb == 10.5
        assert metadata.feature_count == 20


class TestModelCache:
    """Test the ModelCache class"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.cache = ModelCache(max_size=3, max_memory_gb=1.0)
    
    def test_cache_put_and_get(self):
        """Test putting and getting models from cache"""
        model = MagicMock()
        metadata = ModelMetadata(
            name="test_model",
            path="/test/path.pkl",
            model_type="TestModel",
            version="1.0",
            created_at=datetime.now(),
            last_used=datetime.now()
        )
        
        # Put model in cache
        self.cache.put("test_key", model, metadata)
        
        # Get model from cache
        cached_model = self.cache.get("test_key")
        
        assert cached_model == model
        assert "test_key" in self.cache.cache
        assert "test_key" in self.cache.metadata
        assert "test_key" in self.cache.access_times
    
    def test_cache_miss(self):
        """Test cache miss"""
        result = self.cache.get("nonexistent_key")
        assert result is None
    
    def test_cache_lru_eviction(self):
        """Test LRU eviction when cache is full"""
        models = [MagicMock() for _ in range(4)]
        metadatas = [
            ModelMetadata(
                name=f"model_{i}",
                path=f"/test/model_{i}.pkl",
                model_type="TestModel",
                version="1.0",
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            for i in range(4)
        ]
        
        # Fill cache beyond capacity
        for i in range(4):
            self.cache.put(f"key_{i}", models[i], metadatas[i])
        
        # First model should be evicted
        assert self.cache.get("key_0") is None
        assert self.cache.get("key_1") is not None
        assert self.cache.get("key_2") is not None
        assert self.cache.get("key_3") is not None
        assert len(self.cache.cache) == 3
    
    def test_cache_access_time_update(self):
        """Test that access times are updated on get"""
        model = MagicMock()
        metadata = ModelMetadata(
            name="test_model",
            path="/test/path.pkl",
            model_type="TestModel",
            version="1.0",
            created_at=datetime.now(),
            last_used=datetime.now()
        )
        
        self.cache.put("test_key", model, metadata)
        initial_time = self.cache.access_times["test_key"]
        
        # Small delay to ensure time difference
        import time
        time.sleep(0.01)
        
        self.cache.get("test_key")
        updated_time = self.cache.access_times["test_key"]
        
        assert updated_time > initial_time
    
    def test_cache_remove(self):
        """Test removing models from cache"""
        model = MagicMock()
        metadata = ModelMetadata(
            name="test_model",
            path="/test/path.pkl",
            model_type="TestModel",
            version="1.0",
            created_at=datetime.now(),
            last_used=datetime.now()
        )
        
        self.cache.put("test_key", model, metadata)
        assert self.cache.get("test_key") is not None
        
        self.cache.remove("test_key")
        assert self.cache.get("test_key") is None
        assert "test_key" not in self.cache.cache
    
    def test_cache_clear(self):
        """Test clearing the cache"""
        models = [MagicMock() for _ in range(2)]
        metadatas = [
            ModelMetadata(
                name=f"model_{i}",
                path=f"/test/model_{i}.pkl",
                model_type="TestModel",
                version="1.0",
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            for i in range(2)
        ]
        
        for i in range(2):
            self.cache.put(f"key_{i}", models[i], metadatas[i])
        
        assert len(self.cache.cache) == 2
        
        self.cache.clear()
        
        assert len(self.cache.cache) == 0
        assert len(self.cache.metadata) == 0
        assert len(self.cache.access_times) == 0
    
    def test_cache_stats(self):
        """Test getting cache statistics"""
        model = MagicMock()
        metadata = ModelMetadata(
            name="test_model",
            path="/test/path.pkl",
            model_type="TestModel",
            version="1.0",
            created_at=datetime.now(),
            last_used=datetime.now()
        )
        
        self.cache.put("test_key", model, metadata)
        
        stats = self.cache.get_stats()
        
        assert stats["size"] == 1
        assert stats["max_size"] == 3
        assert "memory_usage_mb" in stats
        assert stats["max_memory_gb"] == 1.0
        assert "test_key" in stats["models"]


class TestModelManager:
    """Test the ModelManager class"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.temp_dir = tempfile.mkdtemp()
        self.models_dir = Path(self.temp_dir) / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # Create a simple test model
        from sklearn.linear_model import LinearRegression
        self.test_model = LinearRegression()
        self.test_model.fit([[1], [2], [3]], [1, 2, 3])
        
        self.test_model_path = self.models_dir / "test_model.pkl"
        joblib.dump(self.test_model, self.test_model_path)
    
    def teardown_method(self):
        """Cleanup after each test method"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('core.model_manager.config_manager')
    def test_model_manager_initialization(self, mock_config_manager):
        """Test ModelManager initialization"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        assert manager.models_dir == Path(str(self.models_dir))
        assert isinstance(manager.cache, ModelCache)
    
    @patch('core.model_manager.config_manager')
    def test_load_model_success(self, mock_config_manager):
        """Test successful model loading"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        # Load the test model
        loaded_model = manager.load_model(str(self.test_model_path))
        
        assert loaded_model is not None
        assert hasattr(loaded_model, 'predict')
        
        # Test prediction to ensure model works
        prediction = loaded_model.predict([[4]])
        assert len(prediction) == 1
    
    @patch('core.model_manager.config_manager')
    def test_load_nonexistent_model(self, mock_config_manager):
        """Test loading nonexistent model"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        with pytest.raises(ModelError) as exc_info:
            manager.load_model("/nonexistent/model.pkl")
        
        assert "Model file not found" in str(exc_info.value)
    
    @patch('core.model_manager.config_manager')
    def test_model_caching(self, mock_config_manager):
        """Test model caching functionality"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        # Load model twice
        model1 = manager.load_model(str(self.test_model_path))
        model2 = manager.load_model(str(self.test_model_path))
        
        # Should be the same object from cache
        assert model1 is model2
    
    @patch('core.model_manager.config_manager')
    def test_predict_with_numpy_array(self, mock_config_manager):
        """Test prediction with numpy array"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        features = np.array([[4], [5]])
        predictions = manager.predict(str(self.test_model_path), features)
        
        assert isinstance(predictions, np.ndarray)
        assert len(predictions) == 2
    
    @patch('core.model_manager.config_manager')
    def test_predict_with_pandas_dataframe(self, mock_config_manager):
        """Test prediction with pandas DataFrame"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        features = pd.DataFrame([[4], [5]], columns=['feature'])
        predictions = manager.predict(str(self.test_model_path), features)
        
        assert isinstance(predictions, np.ndarray)
        assert len(predictions) == 2
    
    @patch('core.model_manager.config_manager')
    def test_predict_with_invalid_features(self, mock_config_manager):
        """Test prediction with invalid features"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        with pytest.raises(ValidationError) as exc_info:
            manager.predict(str(self.test_model_path), "invalid_features")
        
        assert "Features must be numpy array or pandas DataFrame" in str(exc_info.value)
    
    @patch('core.model_manager.config_manager')
    def test_health_check(self, mock_config_manager):
        """Test model manager health check"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        health = manager.health_check()
        
        assert health["status"] == "healthy"
        assert "model_count" in health
        assert "cache_stats" in health
        assert health["models_directory_exists"] is True
    
    @patch('core.model_manager.config_manager')
    def test_cleanup_cache(self, mock_config_manager):
        """Test cache cleanup"""
        mock_config = MagicMock()
        mock_config.models_directory = str(self.models_dir)
        mock_config.cache_size = 10
        mock_config.max_memory_usage = 1.0
        mock_config.lazy_loading = True
        mock_config_manager.get_section.return_value = mock_config
        
        manager = ModelManager()
        
        # Load a model to populate cache
        manager.load_model(str(self.test_model_path))
        
        # Verify cache has content
        stats_before = manager.get_cache_stats()
        assert stats_before["size"] > 0
        
        # Cleanup cache
        manager.cleanup_cache()
        
        # Verify cache is empty
        stats_after = manager.get_cache_stats()
        assert stats_after["size"] == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
