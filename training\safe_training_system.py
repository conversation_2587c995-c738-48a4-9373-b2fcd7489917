import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class SafeTrainingSystem:
    """PyTorch-free training system using sklearn, XGBoost, and LightGBM"""
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.feature_names = []
        
    def train_ensemble(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Train an ensemble of models"""
        print("Training ensemble models...")
        
        # 1. Random Forest
        print("  Training Random Forest...")
        rf_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            random_state=42,
            n_jobs=-1
        )
        rf_model.fit(X, y)
        self.models['RandomForest'] = rf_model
        
        # 2. XGBoost
        print("  Training XGBoost...")
        xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        )
        xgb_model.fit(X, y)
        self.models['XGBoost'] = xgb_model
        
        # 3. LightGBM
        print("  Training LightGBM...")
        lgb_model = lgb.LGBMClassifier(
            n_estimators=100,
            num_leaves=31,
            learning_rate=0.1,
            feature_fraction=0.8,
            bagging_fraction=0.8,
            random_state=42,
            n_jobs=-1,
            verbosity=-1
        )
        lgb_model.fit(X, y)
        self.models['LightGBM'] = lgb_model
        
        # 4. Logistic Regression (baseline)
        print("  Training Logistic Regression...")
        lr_model = LogisticRegression(
            max_iter=1000,
            random_state=42,
            n_jobs=-1
        )
        lr_model.fit(X, y)
        self.models['LogisticRegression'] = lr_model
        
        print("✅ All models trained successfully!")
        return self.models
    
    def predict_ensemble(self, X: np.ndarray) -> List[Dict[str, Any]]:
        """Make predictions using the ensemble"""
        if not self.models:
            raise ValueError("No models trained yet!")
        
        predictions = []
        
        for i in range(len(X)):
            sample = X[i].reshape(1, -1)
            model_predictions = {}
            
            # Get predictions from each model
            for name, model in self.models.items():
                pred = model.predict(sample)[0]
                prob = model.predict_proba(sample)[0]
                model_predictions[name] = {
                    'prediction': int(pred),
                    'confidence': float(max(prob))
                }
            
            # Ensemble voting
            buy_votes = sum(1 for p in model_predictions.values() if p['prediction'] == 1)
            sell_votes = len(model_predictions) - buy_votes
            
            # Determine final signal
            if buy_votes > sell_votes:
                signal = 'BUY'
                confidence = np.mean([p['confidence'] for p in model_predictions.values() 
                                    if p['prediction'] == 1])
            elif sell_votes > buy_votes:
                signal = 'SELL'
                confidence = np.mean([p['confidence'] for p in model_predictions.values() 
                                    if p['prediction'] == 0])
            else:
                signal = 'HOLD'
                confidence = 0.5
            
            predictions.append({
                'signal': signal,
                'confidence': confidence,
                'buy_votes': buy_votes,
                'sell_votes': sell_votes,
                'model_predictions': model_predictions
            })
        
        return predictions
    
    def evaluate_models(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """Evaluate all models"""
        results = {}
        
        for name, model in self.models.items():
            accuracy = model.score(X_test, y_test)
            results[name] = accuracy
            
        return results