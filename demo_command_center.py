"""
Demo script for NoryonAI Trading Command Center
Shows how to use the system programmatically
"""

from training.safe_training_system import SafeTrainingSystem
from training.real_data_loader import RealTradingDataLoader
import numpy as np

def demo_training():
    """Demo: Train models on a small dataset"""
    print("🚀 DEMO: Training Models on Real Data")
    print("=" * 50)
    
    # 1. Load data
    print("\n1. Loading data...")
    loader = RealTradingDataLoader("data/paperswithbacktestStocks-1Min-Price")
    X, y = loader.load_and_prepare(sample_size=10000)  # Small sample for demo
    
    print(f"   ✓ Loaded {len(X)} samples with {X.shape[1]} features")
    
    # 2. Split data
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 3. Train models
    print("\n2. Training ensemble models...")
    trainer = SafeTrainingSystem()
    models = trainer.train_ensemble(X_train, y_train)
    
    # 4. Evaluate
    print("\n3. Evaluating models...")
    results = trainer.evaluate_models(X_test, y_test)
    for name, accuracy in results.items():
        print(f"   {name}: {accuracy:.4f}")
    
    # 5. Make predictions
    print("\n4. Making sample predictions...")
    predictions = trainer.predict_ensemble(X_test[:5])
    
    for i, pred in enumerate(predictions):
        print(f"\n   Sample {i+1}:")
        print(f"     Signal: {pred['signal']}")
        print(f"     Confidence: {pred['confidence']:.2%}")
        print(f"     Votes - Buy: {pred['buy_votes']}, Sell: {pred['sell_votes']}")
    
    return models, trainer

def demo_live_prediction(trainer, scaler=None):
    """Demo: Make a live prediction"""
    print("\n\n🎯 DEMO: Live Trading Signal")
    print("=" * 50)
    
    # Create sample market data (would come from real-time feed)
    sample_features = np.array([[
        0.002,    # returns
        0.0019,   # log_returns
        1.015,    # high_low_ratio
        1.003,    # close_open_ratio
        1.2,      # volume_ratio
        1.01,     # price_to_sma_5
        1.008,    # price_to_sma_10
        1.005,    # price_to_sma_20
        0.998,    # price_to_sma_50
        0.15,     # macd
        0.12,     # macd_signal
        0.03,     # macd_histogram
        55,       # rsi
        0.6,      # bb_position
        2.5,      # atr
        0.7,      # price_position
        11,       # hour
        2,        # day_of_week
        1,        # is_market_open
        0         # symbol_encoded
    ]])
    
    # Scale if scaler available
    if scaler:
        sample_features = scaler.transform(sample_features)
    
    # Get prediction
    prediction = trainer.predict_ensemble(sample_features)[0]
    
    print(f"\n📊 Market Analysis Complete!")
    print(f"   Signal: {prediction['signal']}")
    print(f"   Confidence: {prediction['confidence']:.2%}")
    print(f"\n   Model Votes:")
    for model, vote in prediction['model_predictions'].items():
        signal = 'BUY' if vote['prediction'] == 1 else 'SELL'
        print(f"     {model}: {signal} ({vote['confidence']:.2%})")

if __name__ == "__main__":
    print("=" * 60)
    print("NORYONAI TRADING SYSTEM - DEMO")
    print("=" * 60)
    
    try:
        # Run training demo
        models, trainer = demo_training()
        
        # Run prediction demo
        demo_live_prediction(trainer)
        
        print("\n\n✅ Demo complete! Launch noryonai_command_center.py for full interface.")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\nMake sure you have:")
        print("  1. Installed all requirements: pip install -r requirements.txt")
        print("  2. Have data in: data/paperswithbacktestStocks-1Min-Price/")
        print("  3. PyArrow installed for data loading") 