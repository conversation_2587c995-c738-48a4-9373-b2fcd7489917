#!/usr/bin/env python
"""
Model Performance Monitoring System for NORYONAI

Tracks model performance, detects drift, and provides retraining alerts.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import json
import os
from datetime import datetime, timedelta
import pickle
import warnings
warnings.filterwarnings('ignore')

class ModelPerformanceMonitor:
    """Monitor ML model performance and detect drift"""
    
    def __init__(self, models_path: str = "models/command_center"):
        self.models_path = models_path
        self.performance_history = {}
        self.drift_thresholds = {
            'accuracy_drop': 0.05,  # 5% accuracy drop triggers alert
            'prediction_drift': 0.1,  # 10% change in prediction distribution
            'feature_drift': 0.15     # 15% change in feature distribution
        }
        
    def monitor_model_performance(self, model_name: str, 
                                predictions: np.ndarray,
                                actual_values: np.ndarray,
                                features: np.ndarray,
                                timestamp: datetime = None) -> Dict[str, Any]:
        """Monitor single model performance"""
        
        if timestamp is None:
            timestamp = datetime.now()
            
        # Calculate performance metrics
        metrics = self._calculate_performance_metrics(predictions, actual_values)
        
        # Detect drift
        drift_results = self._detect_drift(model_name, predictions, features)
        
        # Create performance record
        performance_record = {
            'timestamp': timestamp.isoformat(),
            'model_name': model_name,
            'metrics': metrics,
            'drift': drift_results,
            'sample_size': len(predictions),
            'alerts': []
        }
        
        # Check for alerts
        alerts = self._check_alerts(model_name, metrics, drift_results)
        performance_record['alerts'] = alerts
        
        # Store performance history
        if model_name not in self.performance_history:
            self.performance_history[model_name] = []
        self.performance_history[model_name].append(performance_record)
        
        # Save to disk
        self._save_performance_history()
        
        return performance_record
        
    def _calculate_performance_metrics(self, predictions: np.ndarray, 
                                     actual_values: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive performance metrics"""
        
        metrics = {}
        
        # Classification metrics
        if len(np.unique(actual_values)) <= 10:  # Assume classification
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            metrics['accuracy'] = accuracy_score(actual_values, predictions)
            metrics['precision'] = precision_score(actual_values, predictions, average='weighted', zero_division=0)
            metrics['recall'] = recall_score(actual_values, predictions, average='weighted', zero_division=0)
            metrics['f1_score'] = f1_score(actual_values, predictions, average='weighted', zero_division=0)
            
        # Regression metrics
        else:
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            
            metrics['mse'] = mean_squared_error(actual_values, predictions)
            metrics['mae'] = mean_absolute_error(actual_values, predictions)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['r2'] = r2_score(actual_values, predictions)
            
        # Distribution metrics
        metrics['prediction_mean'] = float(np.mean(predictions))
        metrics['prediction_std'] = float(np.std(predictions))
        metrics['actual_mean'] = float(np.mean(actual_values))
        metrics['actual_std'] = float(np.std(actual_values))
        
        return metrics
        
    def _detect_drift(self, model_name: str, 
                     predictions: np.ndarray,
                     features: np.ndarray) -> Dict[str, Any]:
        """Detect various types of drift"""
        
        drift_results = {
            'prediction_drift': 0.0,
            'feature_drift': 0.0,
            'concept_drift': False,
            'data_drift': False
        }
        
        # Get historical data for comparison
        historical_data = self._get_historical_data(model_name)
        
        if historical_data:
            # Prediction drift (distribution change)
            hist_pred_mean = historical_data.get('prediction_mean', np.mean(predictions))
            hist_pred_std = historical_data.get('prediction_std', np.std(predictions))
            
            current_pred_mean = np.mean(predictions)
            current_pred_std = np.std(predictions)
            
            # Calculate drift using statistical distance
            pred_drift = abs(current_pred_mean - hist_pred_mean) / (hist_pred_std + 1e-8)
            drift_results['prediction_drift'] = float(pred_drift)
            
            # Feature drift (simplified - would need more sophisticated methods)
            if 'feature_stats' in historical_data:
                feature_drift = self._calculate_feature_drift(features, historical_data['feature_stats'])
                drift_results['feature_drift'] = feature_drift
                
        # Set drift flags
        drift_results['data_drift'] = drift_results['feature_drift'] > self.drift_thresholds['feature_drift']
        drift_results['concept_drift'] = drift_results['prediction_drift'] > self.drift_thresholds['prediction_drift']
        
        return drift_results
        
    def _calculate_feature_drift(self, current_features: np.ndarray, 
                               historical_stats: Dict[str, float]) -> float:
        """Calculate feature drift using statistical measures"""
        
        current_stats = {
            'mean': np.mean(current_features, axis=0),
            'std': np.std(current_features, axis=0)
        }
        
        # Calculate drift as normalized difference in means
        hist_means = np.array(historical_stats.get('mean', current_stats['mean']))
        hist_stds = np.array(historical_stats.get('std', current_stats['std']))
        
        mean_drift = np.mean(np.abs(current_stats['mean'] - hist_means) / (hist_stds + 1e-8))
        
        return float(mean_drift)
        
    def _get_historical_data(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get historical performance data for comparison"""
        
        if model_name not in self.performance_history:
            return None
            
        history = self.performance_history[model_name]
        if len(history) == 0:
            return None
            
        # Use last 30 days of data as baseline
        cutoff_date = datetime.now() - timedelta(days=30)
        recent_history = [
            record for record in history 
            if datetime.fromisoformat(record['timestamp']) > cutoff_date
        ]
        
        if not recent_history:
            return None
            
        # Calculate average metrics
        avg_metrics = {}
        for key in recent_history[0]['metrics'].keys():
            values = [record['metrics'][key] for record in recent_history]
            avg_metrics[key] = np.mean(values)
            
        return avg_metrics
        
    def _check_alerts(self, model_name: str, 
                     current_metrics: Dict[str, float],
                     drift_results: Dict[str, Any]) -> List[str]:
        """Check for performance alerts"""
        
        alerts = []
        
        # Accuracy drop alert
        historical_data = self._get_historical_data(model_name)
        if historical_data and 'accuracy' in current_metrics:
            hist_accuracy = historical_data.get('accuracy', current_metrics['accuracy'])
            accuracy_drop = hist_accuracy - current_metrics['accuracy']
            
            if accuracy_drop > self.drift_thresholds['accuracy_drop']:
                alerts.append(f"ACCURACY_DROP: {accuracy_drop:.3f} below baseline")
                
        # Drift alerts
        if drift_results['data_drift']:
            alerts.append(f"DATA_DRIFT: Feature distribution changed ({drift_results['feature_drift']:.3f})")
            
        if drift_results['concept_drift']:
            alerts.append(f"CONCEPT_DRIFT: Prediction distribution changed ({drift_results['prediction_drift']:.3f})")
            
        # Performance threshold alerts
        if 'accuracy' in current_metrics and current_metrics['accuracy'] < 0.6:
            alerts.append(f"LOW_ACCURACY: {current_metrics['accuracy']:.3f} below 60%")
            
        return alerts
        
    def _save_performance_history(self):
        """Save performance history to disk"""
        
        os.makedirs(self.models_path, exist_ok=True)
        history_file = os.path.join(self.models_path, "performance_history.json")
        
        # Convert numpy types to native Python types for JSON serialization
        serializable_history = {}
        for model_name, history in self.performance_history.items():
            serializable_history[model_name] = []
            for record in history:
                serializable_record = {}
                for key, value in record.items():
                    if isinstance(value, dict):
                        serializable_record[key] = {k: float(v) if isinstance(v, (np.float32, np.float64)) else v 
                                                  for k, v in value.items()}
                    else:
                        serializable_record[key] = value
                serializable_history[model_name].append(serializable_record)
                
        with open(history_file, 'w') as f:
            json.dump(serializable_history, f, indent=2, default=str)
            
    def load_performance_history(self):
        """Load performance history from disk"""
        
        history_file = os.path.join(self.models_path, "performance_history.json")
        
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                self.performance_history = json.load(f)
                
    def generate_performance_report(self, model_name: str = None) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {},
            'models': {},
            'recommendations': []
        }
        
        models_to_analyze = [model_name] if model_name else list(self.performance_history.keys())
        
        for model in models_to_analyze:
            if model not in self.performance_history:
                continue
                
            history = self.performance_history[model]
            if not history:
                continue
                
            # Analyze recent performance
            recent_records = history[-10:]  # Last 10 records
            
            model_report = {
                'total_evaluations': len(history),
                'recent_performance': self._analyze_recent_performance(recent_records),
                'drift_analysis': self._analyze_drift_trends(recent_records),
                'alerts_summary': self._summarize_alerts(recent_records),
                'recommendations': self._generate_model_recommendations(model, recent_records)
            }
            
            report['models'][model] = model_report
            
        # Generate overall recommendations
        report['recommendations'] = self._generate_overall_recommendations(report['models'])
        
        # Save report
        report_file = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
            
        return report
        
    def _analyze_recent_performance(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze recent performance trends"""
        
        if not records:
            return {}
            
        # Extract metrics over time
        metrics_over_time = {}
        for record in records:
            for metric, value in record['metrics'].items():
                if metric not in metrics_over_time:
                    metrics_over_time[metric] = []
                metrics_over_time[metric].append(value)
                
        # Calculate trends
        analysis = {}
        for metric, values in metrics_over_time.items():
            if len(values) > 1:
                trend = np.polyfit(range(len(values)), values, 1)[0]  # Linear trend
                analysis[metric] = {
                    'current': values[-1],
                    'average': np.mean(values),
                    'trend': 'improving' if trend > 0 else 'declining' if trend < 0 else 'stable',
                    'trend_slope': float(trend)
                }
                
        return analysis
        
    def _analyze_drift_trends(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze drift trends over time"""
        
        drift_analysis = {
            'prediction_drift_trend': [],
            'feature_drift_trend': [],
            'drift_events': 0
        }
        
        for record in records:
            drift = record.get('drift', {})
            drift_analysis['prediction_drift_trend'].append(drift.get('prediction_drift', 0))
            drift_analysis['feature_drift_trend'].append(drift.get('feature_drift', 0))
            
            if drift.get('data_drift', False) or drift.get('concept_drift', False):
                drift_analysis['drift_events'] += 1
                
        return drift_analysis
        
    def _summarize_alerts(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize alerts from recent records"""
        
        alert_summary = {
            'total_alerts': 0,
            'alert_types': {},
            'recent_alerts': []
        }
        
        for record in records:
            alerts = record.get('alerts', [])
            alert_summary['total_alerts'] += len(alerts)
            
            for alert in alerts:
                alert_type = alert.split(':')[0]
                alert_summary['alert_types'][alert_type] = alert_summary['alert_types'].get(alert_type, 0) + 1
                
            if alerts:
                alert_summary['recent_alerts'].extend(alerts)
                
        return alert_summary
        
    def _generate_model_recommendations(self, model_name: str, 
                                      records: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations for specific model"""
        
        recommendations = []
        
        if not records:
            return recommendations
            
        # Check for consistent poor performance
        recent_accuracies = [r['metrics'].get('accuracy', 0.5) for r in records[-5:]]
        if all(acc < 0.65 for acc in recent_accuracies):
            recommendations.append("Model consistently underperforming. Consider retraining with more data.")
            
        # Check for drift
        recent_drift = [r['drift'].get('data_drift', False) for r in records[-3:]]
        if any(recent_drift):
            recommendations.append("Data drift detected. Retrain model with recent data.")
            
        # Check for alert frequency
        recent_alerts = sum(len(r.get('alerts', [])) for r in records[-5:])
        if recent_alerts > 10:
            recommendations.append("High alert frequency. Investigate model stability.")
            
        return recommendations
        
    def _generate_overall_recommendations(self, models_analysis: Dict[str, Any]) -> List[str]:
        """Generate overall system recommendations"""
        
        recommendations = []
        
        # Check if multiple models need attention
        models_with_issues = 0
        for model_name, analysis in models_analysis.items():
            if analysis.get('alerts_summary', {}).get('total_alerts', 0) > 5:
                models_with_issues += 1
                
        if models_with_issues > len(models_analysis) * 0.5:
            recommendations.append("Multiple models showing issues. Consider systematic review of training pipeline.")
            
        # Check for overall drift trends
        total_drift_events = sum(
            analysis.get('drift_analysis', {}).get('drift_events', 0)
            for analysis in models_analysis.values()
        )
        
        if total_drift_events > 5:
            recommendations.append("High drift activity across models. Market regime may have changed.")
            
        return recommendations

def demo_performance_monitoring():
    """Demonstrate performance monitoring"""
    
    print("📊 MODEL PERFORMANCE MONITORING DEMO")
    print("=" * 60)
    
    # Create monitor
    monitor = ModelPerformanceMonitor()
    
    # Simulate model performance over time
    np.random.seed(42)
    
    for day in range(30):
        # Simulate degrading performance over time
        base_accuracy = 0.75 - (day * 0.005)  # Gradual decline
        noise = np.random.normal(0, 0.02)
        
        # Generate fake predictions and actuals
        n_samples = 100
        predictions = np.random.binomial(1, base_accuracy + noise, n_samples)
        actual_values = np.random.binomial(1, 0.5, n_samples)
        features = np.random.randn(n_samples, 20)
        
        # Monitor performance
        timestamp = datetime.now() - timedelta(days=30-day)
        result = monitor.monitor_model_performance(
            model_name="RandomForest",
            predictions=predictions,
            actual_values=actual_values,
            features=features,
            timestamp=timestamp
        )
        
        if result['alerts']:
            print(f"Day {day+1}: {len(result['alerts'])} alerts - {result['alerts'][0]}")
            
    # Generate report
    report = monitor.generate_performance_report()
    
    print(f"\n📋 PERFORMANCE REPORT SUMMARY:")
    for model_name, analysis in report['models'].items():
        print(f"\n🤖 {model_name}:")
        print(f"   Total Evaluations: {analysis['total_evaluations']}")
        print(f"   Drift Events: {analysis['drift_analysis']['drift_events']}")
        print(f"   Total Alerts: {analysis['alerts_summary']['total_alerts']}")
        print(f"   Recommendations: {len(analysis['recommendations'])}")
        
    print(f"\n🎯 OVERALL RECOMMENDATIONS:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"   {i}. {rec}")

if __name__ == "__main__":
    demo_performance_monitoring() 