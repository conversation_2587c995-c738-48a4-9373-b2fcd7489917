# Kubernetes Secrets for NoryonAI
# Note: In production, use external secret management (e.g., <PERSON><PERSON><PERSON><PERSON><PERSON> Vault, AWS Secrets Manager)

apiVersion: v1
kind: Secret
metadata:
  name: noryonai-secrets
  namespace: noryonai
  labels:
    app: noryonai-backend
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  # To encode: echo -n "your-secret" | base64
  
  # Database password
  db-password: bm9yeW9uYWlfcGFzc3dvcmRfMTIz  # noryonai_password_123
  
  # Application secret key (32+ characters)
  secret-key: bm9yeW9uYWlfcHJvZHVjdGlvbl9zZWNyZXRfa2V5XzIwMjQ=  # noryonai_production_secret_key_2024
  
  # Redis password
  redis-password: cmVkaXNfcGFzc3dvcmRfMTIz  # redis_password_123
  
  # JWT signing key
  jwt-key: and0X3NpZ25pbmdfa2V5XzIwMjQ=  # jwt_signing_key_2024

---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: noryonai-tls
  namespace: noryonai
  labels:
    app: noryonai-backend
type: kubernetes.io/tls
data:
  # Replace with actual TLS certificate and key
  # To generate self-signed for testing:
  # openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout tls.key -out tls.crt
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t  # Base64 encoded certificate
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t  # Base64 encoded private key

---
# Service Account for NoryonAI
apiVersion: v1
kind: ServiceAccount
metadata:
  name: noryonai-service-account
  namespace: noryonai
  labels:
    app: noryonai-backend

---
# Role for NoryonAI Service Account
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: noryonai-role
  namespace: noryonai
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
# Role Binding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: noryonai-role-binding
  namespace: noryonai
subjects:
- kind: ServiceAccount
  name: noryonai-service-account
  namespace: noryonai
roleRef:
  kind: Role
  name: noryonai-role
  apiGroup: rbac.authorization.k8s.io
