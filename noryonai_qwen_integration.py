#!/usr/bin/env python
"""
NORYONAI + Qwen 14B Integration
The complete trading system with local LLM
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import asyncio
from datetime import datetime
import json
import time
from typing import Dict, List, Optional

class QwenTradingBrain:
    """Qwen 14B integrated with NORYONAI"""
    
    def __init__(self):
        self.model_path = "qwen ai"
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.loaded = False
        
    def load_model(self):
        """Load Qwen 14B once"""
        if self.loaded:
            return
            
        print("Loading Qwen 14B for NORYONAI...")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path,
            trust_remote_code=True,
            use_fast=False
        )
        
        # Load model optimized
        if self.device == "cuda":
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                load_in_8bit=True,
                trust_remote_code=True
            )
        else:
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float32,
                device_map="cpu",
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
        self.loaded = True
        print("✅ Qwen 14B ready!")
        
    async def analyze_market(self, market_data: Dict) -> Dict:
        """Analyze market with Qwen"""
        
        if not self.loaded:
            self.load_model()
            
        # Create comprehensive prompt
        prompt = f"""You are an expert forex trader analyzing market data.

Current Market Data:
- Pair: {market_data.get('pair', 'EURUSD')}
- Price: {market_data.get('current_price', 0)}
- RSI: {market_data.get('rsi', 50)}
- MACD: {market_data.get('macd', 'neutral')}
- Pattern: {market_data.get('pattern', 'none detected')}
- Support: {market_data.get('support', 0)}
- Resistance: {market_data.get('resistance', 0)}

Provide a detailed analysis with:
1. Trading Decision: BUY/SELL/HOLD
2. Confidence: 0-100%
3. Entry Price: specific level
4. Stop Loss: specific level
5. Take Profit: specific level
6. Risk/Reward Ratio
7. Brief reasoning

Be specific and precise with numbers."""

        # Generate response
        inputs = self.tokenizer(prompt, return_tensors="pt")
        if self.device == "cuda":
            inputs = inputs.to("cuda")
            
        start_time = time.time()
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=200,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = response.replace(prompt, "").strip()
        
        processing_time = time.time() - start_time
        
        # Parse response
        decision = self.parse_decision(response)
        
        return {
            'llm_name': 'Qwen-14B',
            'decision': decision['action'],
            'confidence': decision['confidence'],
            'reasoning': response,
            'suggested_parameters': {
                'entry': decision.get('entry', market_data.get('current_price', 0)),
                'stop_loss': decision.get('stop_loss', 0.002),
                'take_profit': decision.get('take_profit', 0.004)
            },
            'processing_time': processing_time,
            'cost': 0.0  # FREE!
        }
        
    def parse_decision(self, response: str) -> Dict:
        """Parse Qwen's response"""
        
        response_upper = response.upper()
        
        # Extract decision
        if "BUY" in response_upper and "SELL" not in response_upper:
            action = "buy"
        elif "SELL" in response_upper and "BUY" not in response_upper:
            action = "sell"
        else:
            action = "hold"
            
        # Extract confidence (look for percentages)
        confidence = 0.7  # default
        import re
        conf_match = re.search(r'(\d+)%', response)
        if conf_match:
            confidence = int(conf_match.group(1)) / 100
            
        # Extract price levels (simplified)
        numbers = re.findall(r'\d+\.\d+', response)
        
        result = {
            'action': action,
            'confidence': confidence
        }
        
        # Try to identify entry, stop, target from context
        if len(numbers) >= 3:
            result['entry'] = float(numbers[0])
            result['stop_loss'] = float(numbers[1])
            result['take_profit'] = float(numbers[2])
            
        return result

class EnhancedNORYONAI:
    """Your complete trading system with Qwen 14B"""
    
    def __init__(self):
        # Your 74 AI models (placeholder)
        self.ai_models = self.load_74_models()
        
        # Qwen 14B brain
        self.qwen_brain = QwenTradingBrain()
        
        # Other components
        self.market_memory = {}
        self.active_trades = []
        
    def load_74_models(self):
        """Load your existing 74 models"""
        # Placeholder - connect your actual models here
        return {
            'models_loaded': 74,
            'status': 'ready'
        }
        
    async def make_trading_decision(self, market_data: Dict) -> Dict:
        """Complete trading decision process"""
        
        print(f"\n🔍 Analyzing {market_data.get('pair', 'EURUSD')}...")
        
        # Step 1: Get signals from your 74 models
        ai_signals = self.get_ai_signals(market_data)
        print(f"📊 74 AI Models: {ai_signals['consensus']}")
        
        # Step 2: Get Qwen's analysis
        qwen_analysis = await self.qwen_brain.analyze_market(market_data)
        print(f"🧠 Qwen 14B: {qwen_analysis['decision'].upper()} "
              f"(confidence: {qwen_analysis['confidence']:.1%})")
        
        # Step 3: Combine decisions
        final_decision = self.combine_decisions(ai_signals, qwen_analysis)
        
        # Step 4: Risk check
        if final_decision['action'] != 'hold':
            risk_approved = self.check_risk_parameters(final_decision)
            if not risk_approved:
                print("⚠️  Risk check failed - adjusting position size")
                final_decision['position_size'] *= 0.5
                
        return final_decision
        
    def get_ai_signals(self, market_data: Dict) -> Dict:
        """Get signals from your 74 models"""
        
        # Simulate your 74 models analyzing
        # In reality, this calls your actual models
        
        buy_votes = 45
        sell_votes = 20
        hold_votes = 9
        
        if buy_votes > sell_votes and buy_votes > hold_votes:
            consensus = "buy"
        elif sell_votes > buy_votes and sell_votes > hold_votes:
            consensus = "sell"
        else:
            consensus = "hold"
            
        return {
            'consensus': consensus,
            'buy_votes': buy_votes,
            'sell_votes': sell_votes,
            'hold_votes': hold_votes,
            'confidence': max(buy_votes, sell_votes, hold_votes) / 74
        }
        
    def combine_decisions(self, ai_signals: Dict, qwen_analysis: Dict) -> Dict:
        """Combine 74 models + Qwen for final decision"""
        
        # Weight: 60% your models, 40% Qwen
        ai_weight = 0.6
        qwen_weight = 0.4
        
        # If both agree, high confidence
        if ai_signals['consensus'] == qwen_analysis['decision']:
            action = ai_signals['consensus']
            confidence = (ai_signals['confidence'] * ai_weight + 
                         qwen_analysis['confidence'] * qwen_weight)
        else:
            # Disagreement - be cautious
            if ai_signals['confidence'] > 0.8:
                action = ai_signals['consensus']
            elif qwen_analysis['confidence'] > 0.8:
                action = qwen_analysis['decision']
            else:
                action = 'hold'
            confidence = 0.5
            
        return {
            'action': action,
            'confidence': confidence,
            'entry': qwen_analysis['suggested_parameters']['entry'],
            'stop_loss': qwen_analysis['suggested_parameters']['stop_loss'],
            'take_profit': qwen_analysis['suggested_parameters']['take_profit'],
            'position_size': self.calculate_position_size(confidence),
            'reasoning': {
                'ai_models': f"{ai_signals['buy_votes']} buy, "
                            f"{ai_signals['sell_votes']} sell, "
                            f"{ai_signals['hold_votes']} hold",
                'qwen': qwen_analysis['reasoning'][:200] + "..."
            }
        }
        
    def calculate_position_size(self, confidence: float) -> float:
        """Dynamic position sizing based on confidence"""
        
        base_size = 10000  # Base position size
        
        if confidence > 0.85:
            return base_size * 1.0
        elif confidence > 0.75:
            return base_size * 0.75
        elif confidence > 0.65:
            return base_size * 0.5
        else:
            return base_size * 0.25
            
    def check_risk_parameters(self, decision: Dict) -> bool:
        """Verify risk is acceptable"""
        
        # Check risk/reward ratio
        entry = decision['entry']
        stop = decision['stop_loss']
        target = decision['take_profit']
        
        risk = abs(entry - stop)
        reward = abs(target - entry)
        
        if reward / risk < 1.5:  # Minimum 1.5:1 RR
            return False
            
        return True

# Demo function
async def demo_enhanced_system():
    """Demonstrate the enhanced NORYONAI system"""
    
    print("=" * 60)
    print("🚀 NORYONAI + QWEN 14B TRADING SYSTEM")
    print("=" * 60)
    
    # Initialize system
    system = EnhancedNORYONAI()
    
    # Load Qwen (one time)
    system.qwen_brain.load_model()
    
    # Test market data
    test_data = {
        'pair': 'EURUSD',
        'current_price': 1.0950,
        'rsi': 65,
        'macd': 'bullish',
        'pattern': 'ascending_triangle',
        'support': 1.0920,
        'resistance': 1.0980,
        'volume': 'increasing'
    }
    
    # Make decision
    decision = await system.make_trading_decision(test_data)
    
    # Display results
    print("\n" + "=" * 60)
    print("📋 FINAL TRADING DECISION")
    print("=" * 60)
    
    print(f"\nAction: {decision['action'].upper()}")
    print(f"Confidence: {decision['confidence']:.1%}")
    
    if decision['action'] != 'hold':
        print(f"\nEntry: {decision['entry']}")
        print(f"Stop Loss: {decision['stop_loss']}")
        print(f"Take Profit: {decision['take_profit']}")
        print(f"Position Size: {decision['position_size']} units")
        
    print(f"\nReasoning:")
    print(f"- AI Models: {decision['reasoning']['ai_models']}")
    print(f"- Qwen 14B: {decision['reasoning']['qwen']}")
    
    print("\n✅ System ready for live trading!")

if __name__ == "__main__":
    # Run demo
    asyncio.run(demo_enhanced_system()) 