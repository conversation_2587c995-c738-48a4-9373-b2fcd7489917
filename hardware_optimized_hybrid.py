#!/usr/bin/env python
"""
Hardware-Optimized Hybrid Trading System
Smart resource management for limited hardware
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import asyncio
import psutil
import gc
import os
from typing import Dict, Optional
import time

class ResourceManager:
    """Monitors and manages system resources"""
    
    def __init__(self):
        self.min_free_memory_gb = 4.0  # Keep 4GB free
        self.max_cpu_percent = 80.0
        
    def check_resources(self) -> Dict:
        """Check current system resources"""
        memory = psutil.virtual_memory()
        cpu = psutil.cpu_percent(interval=0.1)
        
        return {
            'memory_available_gb': memory.available / (1024**3),
            'memory_percent': memory.percent,
            'cpu_percent': cpu,
            'can_load_model': memory.available / (1024**3) > 16  # Need 16GB for Qwen
        }
        
    def optimize_memory(self):
        """Free up memory"""
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

class OptimizedHybridBrain:
    """Hardware-aware hybrid trading brain"""
    
    def __init__(self):
        # Resource manager
        self.resources = ResourceManager()
        
        # Models
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.model_loaded = False
        
        # API configuration
        self.api_keys = {
            'openai': os.getenv('OPENAI_API_KEY'),
            'anthropic': os.getenv('ANTHROPIC_API_KEY')
        }
        
        # Smart routing
        self.decision_counts = {
            'local': 0,
            'api': 0,
            'models_only': 0
        }
        
        # Cost control
        self.daily_budget = 5.0
        self.daily_spent = 0.0
        
    def load_qwen_lazy(self):
        """Load Qwen only when needed and resources allow"""
        
        if self.model_loaded:
            return True
            
        # Check resources
        resources = self.resources.check_resources()
        
        if not resources['can_load_model']:
            print("⚠️  Not enough memory for Qwen - using API fallback")
            return False
            
        try:
            print("Loading Qwen 14B (optimized for your hardware)...")
            
            # Load tokenizer first (small)
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                "qwen ai",
                trust_remote_code=True,
                use_fast=False
            )
            
            # Load model with extreme optimization
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                "qwen ai",
                torch_dtype=torch.float32,
                device_map="cpu",
                trust_remote_code=True,
                low_cpu_mem_usage=True,
                max_memory={0: "10GB", "cpu": "20GB"}  # Limit memory usage
            )
            
            self.model_loaded = True
            print("✅ Qwen loaded with optimization!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load Qwen: {e}")
            self.resources.optimize_memory()
            return False
            
    async def smart_decision(self, market_data: Dict, ai_signals: Dict) -> Dict:
        """Ultra-smart routing based on hardware and complexity"""
        
        # Check current resources
        resources = self.resources.check_resources()
        complexity = self.assess_complexity(market_data, ai_signals)
        
        print(f"\n📊 Resources: {resources['memory_available_gb']:.1f}GB free, "
              f"CPU: {resources['cpu_percent']:.0f}%")
        print(f"📈 Complexity: {complexity:.2f}")
        
        # Decision tree
        if ai_signals['confidence'] > 0.85:
            # Very clear - no LLM needed
            self.decision_counts['models_only'] += 1
            return {
                'source': '74-Models',
                'decision': ai_signals['consensus'],
                'confidence': ai_signals['confidence'],
                'cost': 0.0,
                'reason': 'High confidence from 74 models'
            }
            
        elif complexity < 0.5 and resources['memory_available_gb'] > 8:
            # Simple + resources available = use local
            if self.load_qwen_lazy():
                result = await self.local_qwen_analysis(market_data)
                self.decision_counts['local'] += 1
                return result
            else:
                # Fallback to API
                return await self.api_fallback(market_data)
                
        elif complexity > 0.7 and self.daily_spent < self.daily_budget:
            # Complex scenario - use API if budget allows
            self.decision_counts['api'] += 1
            return await self.api_analysis(market_data)
            
        else:
            # Medium complexity or budget exceeded - try local first
            if self.load_qwen_lazy():
                result = await self.local_qwen_analysis(market_data)
                self.decision_counts['local'] += 1
                return result
            else:
                # Last resort - use 74 models with lower confidence
                return {
                    'source': '74-Models-Fallback',
                    'decision': ai_signals['consensus'],
                    'confidence': ai_signals['confidence'] * 0.8,
                    'cost': 0.0,
                    'reason': 'Resource constraints'
                }
                
    def assess_complexity(self, market_data: Dict, ai_signals: Dict) -> float:
        """Enhanced complexity assessment"""
        
        score = 0.0
        
        # Market factors
        if market_data.get('volatility', 0) > 30:
            score += 0.2
        if market_data.get('news_impact') == 'high':
            score += 0.3
        if market_data.get('pattern') in ['unusual', 'complex']:
            score += 0.2
            
        # Signal conflicts
        if ai_signals.get('confidence', 1.0) < 0.6:
            score += 0.3
            
        return min(score, 1.0)
        
    async def local_qwen_analysis(self, market_data: Dict) -> Dict:
        """Optimized local analysis"""
        
        prompt = f"""Quick forex analysis:
{market_data.get('pair')}: {market_data.get('price')}
RSI: {market_data.get('rsi')}
Pattern: {market_data.get('pattern')}

Decision (BUY/SELL/HOLD):"""

        inputs = self.qwen_tokenizer(
            prompt, 
            return_tensors="pt",
            max_length=256,
            truncation=True
        )
        
        start = time.time()
        
        # Generate with constraints
        with torch.no_grad():
            outputs = self.qwen_model.generate(
                **inputs,
                max_new_tokens=50,  # Keep short
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.qwen_tokenizer.eos_token_id
            )
            
        response = self.qwen_tokenizer.decode(outputs[0], skip_special_tokens=True)
        elapsed = time.time() - start
        
        # Free memory immediately
        del outputs
        self.resources.optimize_memory()
        
        return {
            'source': 'Qwen-14B-Local',
            'decision': self.parse_decision(response),
            'confidence': 0.75,
            'cost': 0.0,
            'time': elapsed,
            'reason': response[:100]
        }
        
    async def api_analysis(self, market_data: Dict) -> Dict:
        """API analysis for complex scenarios"""
        
        # Simulate API call
        self.daily_spent += 0.10
        
        return {
            'source': 'GPT-4-API',
            'decision': 'hold',  # Would be actual API response
            'confidence': 0.85,
            'cost': 0.10,
            'time': 1.5,
            'reason': 'Complex market analysis via API'
        }
        
    async def api_fallback(self, market_data: Dict) -> Dict:
        """Fallback when local model can't load"""
        
        if self.daily_spent < self.daily_budget:
            return await self.api_analysis(market_data)
        else:
            # Budget exceeded
            return {
                'source': 'Budget-Limited',
                'decision': 'hold',
                'confidence': 0.5,
                'cost': 0.0,
                'reason': 'Daily API budget exceeded'
            }
            
    def parse_decision(self, text: str) -> str:
        """Extract decision from text"""
        text_upper = text.upper()
        if 'BUY' in text_upper:
            return 'buy'
        elif 'SELL' in text_upper:
            return 'sell'
        return 'hold'
        
    def get_stats(self) -> Dict:
        """Get usage statistics"""
        total = sum(self.decision_counts.values())
        if total == 0:
            return self.decision_counts
            
        return {
            'decisions': self.decision_counts,
            'percentages': {
                k: (v/total)*100 for k, v in self.decision_counts.items()
            },
            'daily_cost': self.daily_spent,
            'budget_remaining': self.daily_budget - self.daily_spent
        }

# Demo
async def demo_optimized_hybrid():
    """Demonstrate hardware-optimized hybrid system"""
    
    print("=" * 60)
    print("🚀 HARDWARE-OPTIMIZED HYBRID TRADING")
    print("=" * 60)
    
    brain = OptimizedHybridBrain()
    
    # Test scenarios
    scenarios = [
        {
            'name': 'Clear trend (uses 74 models only)',
            'market': {'pair': 'EURUSD', 'price': 1.0950, 'rsi': 75},
            'ai_signals': {'consensus': 'buy', 'confidence': 0.92}
        },
        {
            'name': 'Simple analysis (uses local Qwen)',
            'market': {'pair': 'GBPUSD', 'price': 1.2850, 'rsi': 55},
            'ai_signals': {'consensus': 'hold', 'confidence': 0.65}
        },
        {
            'name': 'Complex scenario (uses API)',
            'market': {
                'pair': 'USDJPY', 
                'price': 150.25, 
                'volatility': 45,
                'news_impact': 'high',
                'pattern': 'unusual'
            },
            'ai_signals': {'consensus': 'sell', 'confidence': 0.45}
        }
    ]
    
    for scenario in scenarios:
        print(f"\n\n🎯 {scenario['name']}")
        print("-" * 40)
        
        decision = await brain.smart_decision(
            scenario['market'],
            scenario['ai_signals']
        )
        
        print(f"\n📋 Decision: {decision['decision'].upper()}")
        print(f"🎯 Source: {decision['source']}")
        print(f"💰 Cost: ${decision['cost']:.2f}")
        print(f"📊 Confidence: {decision['confidence']:.1%}")
        print(f"💭 Reason: {decision['reason']}")
    
    # Show stats
    stats = brain.get_stats()
    print("\n\n📊 USAGE STATISTICS")
    print("-" * 40)
    print(f"Models only: {stats['percentages'].get('models_only', 0):.0f}%")
    print(f"Local Qwen: {stats['percentages'].get('local', 0):.0f}%")
    print(f"API calls: {stats['percentages'].get('api', 0):.0f}%")
    print(f"\nDaily cost: ${stats['daily_cost']:.2f}")
    print(f"Budget remaining: ${stats['budget_remaining']:.2f}")

if __name__ == "__main__":
    asyncio.run(demo_optimized_hybrid()) 