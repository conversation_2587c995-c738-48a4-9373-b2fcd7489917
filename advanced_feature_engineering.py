#!/usr/bin/env python
"""
Advanced Feature Engineering Pipeline for NORYONAI

Creates sophisticated features for ML models including technical indicators,
market regime detection, sentiment features, and cross-asset correlations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
# import talib  # Commented out to avoid dependency issues
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureEngineer:
    """Advanced feature engineering for financial time series"""
    
    def __init__(self):
        self.scalers = {}
        self.pca_models = {}
        self.regime_models = {}
        self.feature_importance = {}
        
    def create_comprehensive_features(self, df: pd.DataFrame, 
                                    symbol: str = "UNKNOWN",
                                    include_regime: bool = True,
                                    include_sentiment: bool = False) -> pd.DataFrame:
        """Create comprehensive feature set for ML training"""
        
        print(f"🔧 Engineering features for {symbol}...")
        
        # Ensure required columns
        df = self._ensure_required_columns(df)
        
        # Create base features
        df = self._create_price_features(df)
        df = self._create_volume_features(df)
        df = self._create_technical_indicators(df)
        df = self._create_statistical_features(df)
        df = self._create_time_features(df)
        
        # Advanced features
        if include_regime:
            df = self._create_regime_features(df)
            
        df = self._create_momentum_features(df)
        df = self._create_volatility_features(df)
        df = self._create_microstructure_features(df)
        
        # Cross-sectional features (if multiple symbols)
        df = self._create_relative_features(df)
        
        # Feature interactions
        df = self._create_feature_interactions(df)
        
        # Clean and validate features
        df = self._clean_features(df)
        
        print(f"✅ Created {len(df.columns)} features for {symbol}")
        return df
        
    def _ensure_required_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ensure all required columns exist"""
        
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        
        for col in required_cols:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 1000000  # Default volume
                elif col == 'open':
                    df[col] = df['close'].shift(1).fillna(df['close'])
                elif col == 'high':
                    df[col] = df['close'] * 1.001
                elif col == 'low':
                    df[col] = df['close'] * 0.999
                    
        return df
        
    def _create_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create price-based features"""
        
        # Basic returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Multi-period returns
        for period in [2, 3, 5, 10, 20]:
            df[f'returns_{period}d'] = df['close'].pct_change(period)
            df[f'log_returns_{period}d'] = np.log(df['close'] / df['close'].shift(period))
            
        # Price ratios
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        df['high_close_ratio'] = df['high'] / df['close']
        df['low_close_ratio'] = df['low'] / df['close']
        
        # Price position within day's range
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['price_position'] = df['price_position'].fillna(0.5)
        
        # Gap analysis
        df['gap'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)
        df['gap_filled'] = ((df['low'] <= df['close'].shift(1)) & (df['gap'] > 0)) | \
                          ((df['high'] >= df['close'].shift(1)) & (df['gap'] < 0))
        df['gap_filled'] = df['gap_filled'].astype(int)
        
        return df
        
    def _create_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create volume-based features"""
        
        # Volume moving averages
        for window in [5, 10, 20, 50]:
            df[f'volume_ma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_ma_{window}']
            
        # Volume-price relationships
        df['volume_price_trend'] = df['volume'] * df['returns']
        df['volume_weighted_price'] = (df['volume'] * df['close']).rolling(window=20).sum() / \
                                     df['volume'].rolling(window=20).sum()
        
        # On-Balance Volume (OBV)
        df['obv'] = (df['volume'] * np.sign(df['returns'])).cumsum()
        df['obv_ma'] = df['obv'].rolling(window=20).mean()
        df['obv_signal'] = np.where(df['obv'] > df['obv_ma'], 1, -1)
        
        # Volume Rate of Change
        df['volume_roc'] = df['volume'].pct_change(10)
        
        return df
        
    def _create_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create technical indicators using manual implementations"""
        
        # Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            df[f'price_to_sma_{period}'] = df['close'] / df[f'sma_{period}']
            df[f'price_to_ema_{period}'] = df['close'] / df[f'ema_{period}']
            
        # MACD
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        
        # RSI
        for period in [14, 21]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            df[f'rsi_{period}_oversold'] = (df[f'rsi_{period}'] < 30).astype(int)
            df[f'rsi_{period}_overbought'] = (df[f'rsi_{period}'] > 70).astype(int)
            
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        
        # Stochastic
        low_min = df['low'].rolling(window=14).min()
        high_max = df['high'].rolling(window=14).max()
        df['stoch_k'] = 100 * (df['close'] - low_min) / (high_max - low_min)
        df['stoch_d'] = df['stoch_k'].rolling(window=3).mean()
        df['stoch_cross'] = np.where(df['stoch_k'] > df['stoch_d'], 1, -1)
        
        # Williams %R
        df['williams_r'] = -100 * (high_max - df['close']) / (high_max - low_min)
        
        # Average True Range (ATR)
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(window=14).mean()
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Commodity Channel Index (CCI)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        sma_tp = typical_price.rolling(window=20).mean()
        mad = typical_price.rolling(window=20).apply(lambda x: np.mean(np.abs(x - x.mean())))
        df['cci'] = (typical_price - sma_tp) / (0.015 * mad)
        
        # Money Flow Index (MFI)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        money_flow = typical_price * df['volume']
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(window=14).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(window=14).sum()
        mfi_ratio = positive_flow / negative_flow
        df['mfi'] = 100 - (100 / (1 + mfi_ratio))
        
        # Parabolic SAR (simplified)
        df['sar'] = df['close'].rolling(window=20).mean()  # Simplified version
        df['sar_signal'] = np.where(df['close'] > df['sar'], 1, -1)
        
        # Aroon
        high_idx = df['high'].rolling(window=25).apply(lambda x: x.argmax())
        low_idx = df['low'].rolling(window=25).apply(lambda x: x.argmin())
        df['aroon_up'] = ((25 - high_idx) / 25) * 100
        df['aroon_down'] = ((25 - low_idx) / 25) * 100
        df['aroon_oscillator'] = df['aroon_up'] - df['aroon_down']
        
        return df
        
    def _create_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create statistical features"""
        
        # Rolling statistics
        for window in [5, 10, 20, 50]:
            # Volatility
            df[f'volatility_{window}'] = df['returns'].rolling(window=window).std()
            
        # Now create volatility ratios after all volatilities are created
        for window in [5, 10, 50]:
            df[f'volatility_ratio_{window}'] = df[f'volatility_{window}'] / df['volatility_20']
            
        # Continue with other rolling statistics
        for window in [5, 10, 20, 50]:
            # Skewness and Kurtosis
            df[f'skewness_{window}'] = df['returns'].rolling(window=window).skew()
            df[f'kurtosis_{window}'] = df['returns'].rolling(window=window).kurt()
            
            # Min/Max features
            df[f'high_max_{window}'] = df['high'].rolling(window=window).max()
            df[f'low_min_{window}'] = df['low'].rolling(window=window).min()
            df[f'price_position_{window}'] = (df['close'] - df[f'low_min_{window}']) / \
                                           (df[f'high_max_{window}'] - df[f'low_min_{window}'])
            
        # Z-scores
        df['price_zscore'] = (df['close'] - df['sma_20']) / df['volatility_20']
        df['volume_zscore'] = (df['volume'] - df['volume_ma_20']) / df['volume'].rolling(20).std()
        
        # Autocorrelation
        for lag in [1, 2, 5]:
            df[f'returns_autocorr_{lag}'] = df['returns'].rolling(window=20).apply(
                lambda x: x.autocorr(lag=lag) if len(x) > lag else 0
            )
            
        return df
        
    def _create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create time-based features"""
        
        # Ensure datetime index
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'timestamp' in df.columns:
                df.index = pd.to_datetime(df['timestamp'])
            elif 'date' in df.columns:
                df.index = pd.to_datetime(df['date'])
                
        if isinstance(df.index, pd.DatetimeIndex):
            # Time components
            df['hour'] = df.index.hour
            df['day_of_week'] = df.index.dayofweek
            df['day_of_month'] = df.index.day
            df['month'] = df.index.month
            df['quarter'] = df.index.quarter
            
            # Market session indicators
            df['is_market_open'] = df['hour'].between(9, 16).astype(int)
            df['is_opening_hour'] = (df['hour'] == 9).astype(int)
            df['is_closing_hour'] = (df['hour'] == 16).astype(int)
            df['is_lunch_time'] = df['hour'].between(12, 13).astype(int)
            
            # Day type indicators
            df['is_monday'] = (df['day_of_week'] == 0).astype(int)
            df['is_friday'] = (df['day_of_week'] == 4).astype(int)
            df['is_month_end'] = (df.index.day > 25).astype(int)
            df['is_quarter_end'] = ((df['month'] % 3 == 0) & (df.index.day > 25)).astype(int)
            
        else:
            # Default values if no datetime index
            df['hour'] = 12
            df['day_of_week'] = 2
            df['is_market_open'] = 1
            
        return df
        
    def _create_regime_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create market regime features"""
        
        # Volatility regimes
        df['vol_regime'] = self._detect_volatility_regime(df['returns'])
        
        # Trend regimes
        df['trend_regime'] = self._detect_trend_regime(df['close'])
        
        # Volume regimes
        df['volume_regime'] = self._detect_volume_regime(df['volume'])
        
        # Combined regime
        df['market_regime'] = df['vol_regime'] * 4 + df['trend_regime'] * 2 + df['volume_regime']
        
        return df
        
    def _detect_volatility_regime(self, returns: pd.Series, window: int = 20) -> pd.Series:
        """Detect volatility regime (0: low, 1: medium, 2: high)"""
        
        vol = returns.rolling(window=window).std()
        
        # Calculate rolling quantiles properly
        vol_q33 = vol.rolling(window=252).quantile(0.33)
        vol_q67 = vol.rolling(window=252).quantile(0.67)
        
        regime = pd.Series(1, index=returns.index)  # Default to medium
        regime[vol <= vol_q33] = 0  # Low volatility
        regime[vol >= vol_q67] = 2  # High volatility
        
        return regime
        
    def _detect_trend_regime(self, prices: pd.Series, window: int = 50) -> pd.Series:
        """Detect trend regime (0: downtrend, 1: sideways, 2: uptrend)"""
        
        sma_short = prices.rolling(window=window//2).mean()
        sma_long = prices.rolling(window=window).mean()
        
        trend_strength = (sma_short - sma_long) / sma_long
        
        regime = pd.Series(1, index=prices.index)  # Default to sideways
        regime[trend_strength <= -0.02] = 0  # Downtrend
        regime[trend_strength >= 0.02] = 2   # Uptrend
        
        return regime
        
    def _detect_volume_regime(self, volume: pd.Series, window: int = 20) -> pd.Series:
        """Detect volume regime (0: low, 1: normal, 2: high)"""
        
        vol_ma = volume.rolling(window=window).mean()
        vol_ratio = volume / vol_ma
        
        regime = pd.Series(1, index=volume.index)  # Default to normal
        regime[vol_ratio <= 0.7] = 0  # Low volume
        regime[vol_ratio >= 1.5] = 2  # High volume
        
        return regime
        
    def _create_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create momentum-based features"""
        
        # Rate of Change (ROC)
        for period in [5, 10, 20]:
            df[f'roc_{period}'] = df['close'].pct_change(period)
            
        # Momentum (price difference)
        for period in [5, 10, 20]:
            df[f'momentum_{period}'] = df['close'] - df['close'].shift(period)
            
        # Price momentum vs volume momentum
        df['price_volume_momentum'] = df['roc_10'] * df['volume_roc']
        
        # Acceleration (second derivative)
        df['price_acceleration'] = df['returns'].diff()
        df['volume_acceleration'] = df['volume_roc'].diff()
        
        return df
        
    def _create_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create volatility-based features"""
        
        # GARCH-like features
        df['vol_persistence'] = df['volatility_20'].rolling(window=5).mean()
        df['vol_mean_reversion'] = df['volatility_5'] / df['volatility_20']
        
        # Volatility clustering
        df['vol_cluster'] = (df['volatility_5'] > df['volatility_20']).astype(int)
        
        # Volatility breakouts
        df['vol_breakout'] = (df['volatility_5'] > df['volatility_20'] * 1.5).astype(int)
        
        # Realized vs implied volatility proxy
        df['vol_surprise'] = df['volatility_5'] - df['volatility_20']
        
        return df
        
    def _create_microstructure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create microstructure features"""
        
        # Bid-ask spread proxy (using high-low)
        df['spread_proxy'] = (df['high'] - df['low']) / df['close']
        df['spread_ma'] = df['spread_proxy'].rolling(window=20).mean()
        df['spread_ratio'] = df['spread_proxy'] / df['spread_ma']
        
        # Price impact proxy
        df['price_impact'] = abs(df['returns']) / (df['volume'] / df['volume_ma_20'])
        
        # Market efficiency proxy
        df['efficiency'] = abs(df['returns']) / df['volatility_20']
        
        # Tick direction (simplified)
        df['tick_direction'] = np.sign(df['close'].diff())
        df['tick_persistence'] = df['tick_direction'].rolling(window=5).sum()
        
        return df
        
    def _create_relative_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create relative/cross-sectional features"""
        
        # Relative strength vs market (simplified - would need market index)
        market_proxy = df['close'].rolling(window=252).mean()
        df['relative_strength'] = df['close'] / market_proxy
        df['relative_momentum'] = df['returns'] - df['returns'].rolling(window=20).mean()
        
        # Sector rotation proxy (simplified)
        df['sector_momentum'] = df['returns'].rolling(window=5).mean()
        
        return df
        
    def _create_feature_interactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create feature interactions"""
        
        # Volume-price interactions
        df['vol_price_interaction'] = df['volume_ratio_20'] * df['price_position']
        df['vol_momentum_interaction'] = df['volume_ratio_20'] * df['roc_10']
        
        # Volatility-momentum interactions
        df['vol_mom_interaction'] = df['volatility_20'] * df['momentum_10']
        
        # Regime interactions
        if 'market_regime' in df.columns:
            df['regime_momentum'] = df['market_regime'] * df['roc_10']
            df['regime_volatility'] = df['market_regime'] * df['volatility_20']
            
        # Technical indicator interactions
        df['rsi_bb_interaction'] = df['rsi_14'] * df['bb_position']
        df['macd_rsi_interaction'] = df['macd'] * df['rsi_14']
        
        return df
        
    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate features"""
        
        # Replace infinite values
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # Forward fill then backward fill
        df = df.fillna(method='ffill').fillna(method='bfill')
        
        # Fill remaining NaN with 0
        df = df.fillna(0)
        
        # Remove features with too many missing values (>50%)
        missing_pct = df.isnull().sum() / len(df)
        features_to_drop = missing_pct[missing_pct > 0.5].index
        if len(features_to_drop) > 0:
            print(f"⚠️ Dropping {len(features_to_drop)} features with >50% missing values")
            df = df.drop(columns=features_to_drop)
            
        return df
        
    def create_target_variable(self, df: pd.DataFrame, 
                             target_type: str = "classification",
                             horizon: int = 1,
                             threshold: float = 0.001) -> pd.Series:
        """Create target variable for ML training"""
        
        if target_type == "classification":
            # Binary classification: price goes up (1) or down (0)
            future_returns = df['close'].pct_change(horizon).shift(-horizon)
            target = (future_returns > threshold).astype(int)
            
        elif target_type == "regression":
            # Regression: predict future returns
            target = df['close'].pct_change(horizon).shift(-horizon)
            
        elif target_type == "multiclass":
            # Multi-class: strong down (-1), neutral (0), strong up (1)
            future_returns = df['close'].pct_change(horizon).shift(-horizon)
            target = pd.Series(0, index=df.index)
            target[future_returns <= -threshold] = -1
            target[future_returns >= threshold] = 1
            
        else:
            raise ValueError(f"Unknown target_type: {target_type}")
            
        return target
        
    def get_feature_importance(self, feature_names: List[str], 
                             model_importances: Dict[str, np.ndarray]) -> pd.DataFrame:
        """Calculate feature importance across models"""
        
        importance_df = pd.DataFrame(index=feature_names)
        
        for model_name, importances in model_importances.items():
            importance_df[model_name] = importances
            
        # Calculate average importance
        importance_df['average'] = importance_df.mean(axis=1)
        importance_df = importance_df.sort_values('average', ascending=False)
        
        return importance_df

def demo_feature_engineering():
    """Demonstrate advanced feature engineering"""
    
    print("🔧 ADVANCED FEATURE ENGINEERING DEMO")
    print("=" * 60)
    
    # Generate sample data
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
    np.random.seed(42)
    
    # Simulate realistic price data
    returns = np.random.normal(0.0005, 0.02, len(dates))
    prices = 100 * np.exp(np.cumsum(returns))
    
    sample_data = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.001, len(dates))),
        'high': prices * (1 + abs(np.random.normal(0, 0.005, len(dates)))),
        'low': prices * (1 - abs(np.random.normal(0, 0.005, len(dates)))),
        'close': prices,
        'volume': np.random.lognormal(15, 0.5, len(dates))
    }, index=dates)
    
    # Create feature engineer
    engineer = AdvancedFeatureEngineer()
    
    # Generate features
    features_df = engineer.create_comprehensive_features(
        sample_data, 
        symbol="DEMO",
        include_regime=True
    )
    
    # Create target
    target = engineer.create_target_variable(
        features_df, 
        target_type="classification",
        threshold=0.01
    )
    
    # Display results
    print(f"\n📊 FEATURE ENGINEERING RESULTS:")
    print(f"   Original columns: {len(sample_data.columns)}")
    print(f"   Generated features: {len(features_df.columns)}")
    print(f"   Target variable: {target.name if hasattr(target, 'name') else 'target'}")
    print(f"   Data shape: {features_df.shape}")
    
    # Show feature categories
    feature_categories = {
        'Price': [col for col in features_df.columns if any(word in col for word in ['price', 'close', 'open', 'high', 'low'])],
        'Volume': [col for col in features_df.columns if 'volume' in col],
        'Technical': [col for col in features_df.columns if any(word in col for word in ['sma', 'ema', 'rsi', 'macd', 'bb'])],
        'Statistical': [col for col in features_df.columns if any(word in col for word in ['volatility', 'skew', 'kurt', 'zscore'])],
        'Regime': [col for col in features_df.columns if 'regime' in col],
        'Time': [col for col in features_df.columns if any(word in col for word in ['hour', 'day', 'month', 'is_'])]
    }
    
    print(f"\n📈 FEATURE CATEGORIES:")
    for category, features in feature_categories.items():
        print(f"   {category}: {len(features)} features")
        
    # Save sample features
    features_df.to_csv('sample_features.csv')
    print(f"\n💾 Sample features saved to 'sample_features.csv'")
    
    return features_df, target

if __name__ == "__main__":
    demo_feature_engineering() 