import pyarrow as pa
import pyarrow.parquet as pq
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import os
from datetime import datetime
import glob
import json
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class RealTradingDataLoader:
    """Load and preprocess real trading data from arrow/parquet files"""
    
    def __init__(self, data_path: str):
        self.data_path = data_path
        self.scaler = StandardScaler()
        self.symbol_encodings = {}
        
    def load_arrow_files(self, directory: str, sample_size: Optional[int] = None) -> pd.DataFrame:
        """Load data from arrow files"""
        print(f"Loading arrow files from {directory}")
        
        arrow_files = glob.glob(os.path.join(directory, "*.arrow"))
        print(f"Found {len(arrow_files)} arrow files")
        
        dataframes = []
        total_rows = 0
        
        for i, file_path in enumerate(arrow_files):
            print(f"Loading file {i+1}/{len(arrow_files)}: {os.path.basename(file_path)}")
            
            # Read arrow file
            table = pa.ipc.open_file(file_path).read_all()
            df = table.to_pandas()
            
            dataframes.append(df)
            total_rows += len(df)
            
            # Early stopping if we have enough samples
            if sample_size and total_rows >= sample_size:
                break
                
        # Combine all dataframes
        combined_df = pd.concat(dataframes, ignore_index=True)
        
        # Sample if requested
        if sample_size and len(combined_df) > sample_size:
            combined_df = combined_df.sample(n=sample_size, random_state=42)
            
        print(f"Loaded {len(combined_df)} records")
        return combined_df
    
    def load_parquet_files(self, directory: str, sample_size: Optional[int] = None) -> pd.DataFrame:
        """Load data from parquet files"""
        print(f"Loading parquet files from {directory}")
        
        parquet_files = glob.glob(os.path.join(directory, "*.parquet"))
        print(f"Found {len(parquet_files)} parquet files")
        
        dataframes = []
        
        for file_path in parquet_files:
            df = pd.read_parquet(file_path)
            dataframes.append(df)
            
        combined_df = pd.concat(dataframes, ignore_index=True)
        
        if sample_size and len(combined_df) > sample_size:
            combined_df = combined_df.sample(n=sample_size, random_state=42)
            
        return combined_df
    
    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create technical indicators and features"""
        print("Engineering features...")
        
        # Sort by symbol and datetime
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values(['symbol', 'datetime'])
        
        features = []
        
        for symbol in df['symbol'].unique():
            symbol_data = df[df['symbol'] == symbol].copy()
            
            # Price features
            symbol_data['returns'] = symbol_data['adj_close'].pct_change()
            symbol_data['log_returns'] = np.log(symbol_data['adj_close'] / symbol_data['adj_close'].shift(1))
            
            # Volatility
            symbol_data['high_low_ratio'] = symbol_data['adj_high'] / symbol_data['adj_low']
            symbol_data['close_open_ratio'] = symbol_data['adj_close'] / symbol_data['adj_open']
            
            # Volume features
            symbol_data['volume_ma_5'] = symbol_data['volume'].rolling(window=5).mean()
            symbol_data['volume_ratio'] = symbol_data['volume'] / symbol_data['volume_ma_5']
            
            # Price moving averages
            for window in [5, 10, 20, 50]:
                symbol_data[f'sma_{window}'] = symbol_data['adj_close'].rolling(window=window).mean()
                symbol_data[f'price_to_sma_{window}'] = symbol_data['adj_close'] / symbol_data[f'sma_{window}']
            
            # Exponential moving averages
            for span in [12, 26]:
                symbol_data[f'ema_{span}'] = symbol_data['adj_close'].ewm(span=span, adjust=False).mean()
            
            # MACD
            symbol_data['macd'] = symbol_data['ema_12'] - symbol_data['ema_26']
            symbol_data['macd_signal'] = symbol_data['macd'].ewm(span=9, adjust=False).mean()
            symbol_data['macd_histogram'] = symbol_data['macd'] - symbol_data['macd_signal']
            
            # RSI
            delta = symbol_data['adj_close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            symbol_data['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            symbol_data['bb_middle'] = symbol_data['adj_close'].rolling(window=20).mean()
            bb_std = symbol_data['adj_close'].rolling(window=20).std()
            symbol_data['bb_upper'] = symbol_data['bb_middle'] + (bb_std * 2)
            symbol_data['bb_lower'] = symbol_data['bb_middle'] - (bb_std * 2)
            symbol_data['bb_position'] = (symbol_data['adj_close'] - symbol_data['bb_lower']) / (symbol_data['bb_upper'] - symbol_data['bb_lower'])
            
            # Average True Range (ATR)
            high_low = symbol_data['adj_high'] - symbol_data['adj_low']
            high_close = np.abs(symbol_data['adj_high'] - symbol_data['adj_close'].shift())
            low_close = np.abs(symbol_data['adj_low'] - symbol_data['adj_close'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            symbol_data['atr'] = true_range.rolling(window=14).mean()
            
            # Support and Resistance levels
            symbol_data['resistance_20'] = symbol_data['adj_high'].rolling(window=20).max()
            symbol_data['support_20'] = symbol_data['adj_low'].rolling(window=20).min()
            symbol_data['price_position'] = (symbol_data['adj_close'] - symbol_data['support_20']) / (symbol_data['resistance_20'] - symbol_data['support_20'])
            
            # Time features
            symbol_data['hour'] = symbol_data['datetime'].dt.hour
            symbol_data['day_of_week'] = symbol_data['datetime'].dt.dayofweek
            symbol_data['is_market_open'] = symbol_data['hour'].between(9, 16)
            
            features.append(symbol_data)
        
        result_df = pd.concat(features, ignore_index=True)
        
        # Drop NaN values created by rolling windows
        result_df = result_df.dropna()
        
        print(f"Created {len(result_df.columns) - len(df.columns)} new features")
        return result_df
    
    def prepare_training_data(self, df: pd.DataFrame, target_minutes: int = 5) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and targets for training"""
        print(f"Preparing training data with {target_minutes}-minute forward prediction...")
        
        # Create target: future price direction
        df = df.sort_values(['symbol', 'datetime'])
        
        # Group by symbol to calculate future returns
        grouped = df.groupby('symbol')
        
        # Future return after target_minutes
        df['future_return'] = grouped['adj_close'].shift(-target_minutes) / df['adj_close'] - 1
        
        # Classification target: 1 if price goes up, 0 if down
        df['target'] = (df['future_return'] > 0).astype(int)
        
        # Multi-class target for more granular predictions
        df['target_multiclass'] = pd.cut(
            df['future_return'],
            bins=[-np.inf, -0.002, -0.0005, 0.0005, 0.002, np.inf],
            labels=[0, 1, 2, 3, 4]  # strong sell, sell, hold, buy, strong buy
        )
        
        # Drop rows with NaN targets
        df = df.dropna(subset=['target', 'future_return'])
        
        # Select features for training
        feature_columns = [
            'returns', 'log_returns', 'high_low_ratio', 'close_open_ratio',
            'volume_ratio', 'price_to_sma_5', 'price_to_sma_10', 'price_to_sma_20',
            'price_to_sma_50', 'macd', 'macd_signal', 'macd_histogram',
            'rsi', 'bb_position', 'atr', 'price_position',
            'hour', 'day_of_week', 'is_market_open'
        ]
        
        # Only use columns that exist
        available_features = [col for col in feature_columns if col in df.columns]
        
        # Encode symbol as numeric
        for i, symbol in enumerate(df['symbol'].unique()):
            self.symbol_encodings[symbol] = i
        df['symbol_encoded'] = df['symbol'].map(self.symbol_encodings)
        available_features.append('symbol_encoded')
        
        # Extract features and targets
        X = df[available_features].values
        y = df['target'].values
        
        # Scale features
        X = self.scaler.fit_transform(X)
        
        print(f"Prepared {len(X)} samples with {X.shape[1]} features")
        print(f"Target distribution: {np.bincount(y)}")
        
        return X, y
    
    def load_and_prepare(self, sample_size: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """Load data and prepare for training"""
        # Try to load from arrow files first
        train_dir = os.path.join(self.data_path, "train")
        
        if os.path.exists(train_dir):
            df = self.load_arrow_files(train_dir, sample_size)
        else:
            # Try parquet files
            df = self.load_parquet_files(self.data_path, sample_size)
        
        # Engineer features
        df = self.engineer_features(df)
        
        # Prepare training data
        X, y = self.prepare_training_data(df)
        
        return X, y

# Test the loader
if __name__ == "__main__":
    loader = RealTradingDataLoader("data/paperswithbacktestStocks-1Min-Price")
    
    # Load a sample for testing (100k records)
    X, y = loader.load_and_prepare(sample_size=100000)
    
    print(f"\nData loaded successfully!")
    print(f"Features shape: {X.shape}")
    print(f"Targets shape: {y.shape}")
    print(f"Class distribution: {np.bincount(y)}") 