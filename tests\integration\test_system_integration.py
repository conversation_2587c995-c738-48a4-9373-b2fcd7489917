"""
Integration tests for the enhanced NoryonAI system.
Tests the interaction between different system components.
"""

import pytest
import tempfile
import numpy as np
import pandas as pd
import joblib
from pathlib import Path
from unittest.mock import patch
import yaml

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from core.config_manager import ConfigManager, SystemConfig
from core.logging_system import LoggingManager, get_logger
from core.model_manager import ModelManager
from core.exceptions import SystemError, ModelError


class TestSystemIntegration:
    """Test integration between core system components"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.models_dir = Path(self.temp_dir) / "models"
        self.logs_dir = Path(self.temp_dir) / "logs"
        
        # Create directories
        self.config_dir.mkdir(exist_ok=True)
        self.models_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # Create test configuration
        self.create_test_config()
        
        # Create test model
        self.create_test_model()
    
    def teardown_method(self):
        """Cleanup after each test method"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_config(self):
        """Create test configuration file"""
        config_data = {
            'environment': 'testing',
            'debug': True,
            'version': '2.0.0',
            'database': {
                'host': 'localhost',
                'port': 5432,
                'database': 'test_noryonai',
                'pool_size': 5
            },
            'models': {
                'models_directory': str(self.models_dir),
                'cache_size': 10,
                'lazy_loading': True,
                'max_memory_usage': 1.0
            },
            'trading': {
                'initial_capital': 50000.0,
                'max_position_size': 0.05,
                'risk_threshold': 0.02,
                'min_confidence': 0.7
            },
            'logging': {
                'level': 'DEBUG',
                'log_to_file': True,
                'log_file': str(self.logs_dir / 'test.log'),
                'log_rotation': True,
                'max_file_size_mb': 5,
                'backup_count': 3
            },
            'security': {
                'secret_key': 'test_secret_key_for_testing_only_32chars',
                'jwt_expiry': 1800,
                'rate_limit_per_minute': 50
            }
        }
        
        config_file = self.config_dir / "config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(config_data, f)
    
    def create_test_model(self):
        """Create a test model for integration testing"""
        from sklearn.ensemble import RandomForestClassifier
        
        # Create and train a simple model
        X = np.random.rand(100, 5)
        y = np.random.randint(0, 2, 100)
        
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        model.fit(X, y)
        
        # Save the model
        model_path = self.models_dir / "test_classifier.pkl"
        joblib.dump(model, model_path)
        
        return str(model_path)
    
    def test_config_and_logging_integration(self):
        """Test integration between configuration and logging systems"""
        # Initialize configuration manager
        config_manager = ConfigManager(str(self.config_dir))
        
        # Verify configuration loaded correctly
        config = config_manager.get_config()
        assert config.environment.value == 'testing'
        assert config.models.models_directory == str(self.models_dir)
        
        # Initialize logging with the configuration
        with patch('core.logging_system.config_manager', config_manager):
            logging_manager = LoggingManager()
            logging_manager.setup_logging()
            
            # Test that logging is working
            logger = logging_manager.get_logger("integration_test")
            logger.info("Integration test log message")
            
            # Verify log file was created
            log_file = Path(config.logging.log_file)
            assert log_file.exists()
    
    def test_config_and_model_manager_integration(self):
        """Test integration between configuration and model management"""
        # Initialize configuration manager
        config_manager = ConfigManager(str(self.config_dir))
        
        # Initialize model manager with the configuration
        with patch('core.model_manager.config_manager', config_manager):
            model_manager = ModelManager()
            
            # Verify model manager uses correct configuration
            assert str(model_manager.models_dir) == str(self.models_dir)
            assert model_manager.cache.max_size == 10
            assert model_manager.cache.max_memory_gb == 1.0
            
            # Test model loading
            model_path = self.create_test_model()
            loaded_model = model_manager.load_model(model_path)
            
            assert loaded_model is not None
            assert hasattr(loaded_model, 'predict')
    
    def test_full_system_workflow(self):
        """Test a complete workflow using all system components"""
        # 1. Initialize configuration
        config_manager = ConfigManager(str(self.config_dir))
        config = config_manager.get_config()
        
        # 2. Initialize logging
        with patch('core.logging_system.config_manager', config_manager):
            logging_manager = LoggingManager()
            logging_manager.setup_logging()
            logger = logging_manager.get_logger("workflow_test")
            perf_logger = logging_manager.get_performance_logger()
            
            # 3. Initialize model manager
            with patch('core.model_manager.config_manager', config_manager):
                model_manager = ModelManager()
                
                # 4. Perform a complete ML workflow
                logger.info("Starting ML workflow")
                
                # Load model
                model_path = self.create_test_model()
                with perf_logger.timer("model_loading"):
                    model = model_manager.load_model(model_path)
                
                # Make predictions
                test_features = np.random.rand(10, 5)
                with perf_logger.timer("model_prediction"):
                    predictions = model_manager.predict(model_path, test_features)
                
                # Verify results
                assert len(predictions) == 10
                assert all(pred in [0, 1] for pred in predictions)
                
                logger.info(f"Workflow completed with {len(predictions)} predictions")
                
                # 5. Check system health
                health = model_manager.health_check()
                assert health["status"] == "healthy"
    
    def test_error_handling_integration(self):
        """Test error handling across system components"""
        config_manager = ConfigManager(str(self.config_dir))
        
        with patch('core.logging_system.config_manager', config_manager):
            logging_manager = LoggingManager()
            logging_manager.setup_logging()
            
            with patch('core.model_manager.config_manager', config_manager):
                model_manager = ModelManager()
                
                # Test error handling in model loading
                with pytest.raises(ModelError) as exc_info:
                    model_manager.load_model("/nonexistent/model.pkl")
                
                # Verify error contains proper context
                error = exc_info.value
                assert "Model file not found" in error.message
                assert "path" in error.context
                assert error.recovery_suggestions is not None
    
    def test_configuration_validation_integration(self):
        """Test configuration validation with other components"""
        # Create invalid configuration
        invalid_config_data = {
            'environment': 'testing',
            'models': {
                'models_directory': '/nonexistent/path',
                'cache_size': -1,  # Invalid
                'max_memory_usage': -1.0  # Invalid
            },
            'trading': {
                'initial_capital': -1000.0,  # Invalid
                'max_position_size': 2.0,  # Invalid
                'min_confidence': 1.5  # Invalid
            }
        }
        
        invalid_config_file = self.config_dir / "invalid_config.yaml"
        with open(invalid_config_file, 'w') as f:
            yaml.dump(invalid_config_data, f)
        
        # Test that validation catches errors
        with pytest.raises(Exception):  # Should raise validation error
            ConfigManager(str(self.config_dir))
    
    def test_performance_monitoring_integration(self):
        """Test performance monitoring across components"""
        config_manager = ConfigManager(str(self.config_dir))
        
        with patch('core.logging_system.config_manager', config_manager):
            logging_manager = LoggingManager()
            logging_manager.setup_logging()
            perf_logger = logging_manager.get_performance_logger()
            
            with patch('core.model_manager.config_manager', config_manager):
                model_manager = ModelManager()
                
                # Monitor model operations
                model_path = self.create_test_model()
                
                with perf_logger.timer("full_prediction_workflow"):
                    # Load model (should be cached)
                    model = model_manager.load_model(model_path)
                    
                    # Make multiple predictions
                    for i in range(5):
                        features = np.random.rand(10, 5)
                        predictions = model_manager.predict(model_path, features)
                        
                        perf_logger.log_metric(
                            "prediction_count",
                            len(predictions),
                            batch_id=i
                        )
                
                # Verify cache is working
                cache_stats = model_manager.get_cache_stats()
                assert cache_stats["size"] > 0
    
    def test_concurrent_operations(self):
        """Test system behavior under concurrent operations"""
        import threading
        import time
        
        config_manager = ConfigManager(str(self.config_dir))
        
        with patch('core.logging_system.config_manager', config_manager):
            logging_manager = LoggingManager()
            logging_manager.setup_logging()
            
            with patch('core.model_manager.config_manager', config_manager):
                model_manager = ModelManager()
                model_path = self.create_test_model()
                
                results = []
                errors = []
                
                def worker_function(worker_id):
                    try:
                        # Each worker loads model and makes predictions
                        features = np.random.rand(5, 5)
                        predictions = model_manager.predict(model_path, features)
                        results.append((worker_id, len(predictions)))
                    except Exception as e:
                        errors.append((worker_id, str(e)))
                
                # Start multiple worker threads
                threads = []
                for i in range(5):
                    thread = threading.Thread(target=worker_function, args=(i,))
                    threads.append(thread)
                    thread.start()
                
                # Wait for all threads to complete
                for thread in threads:
                    thread.join()
                
                # Verify all operations completed successfully
                assert len(results) == 5
                assert len(errors) == 0
                assert all(count == 5 for _, count in results)


class TestSystemRecovery:
    """Test system recovery and resilience"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.models_dir = Path(self.temp_dir) / "models"
        
        self.config_dir.mkdir(exist_ok=True)
        self.models_dir.mkdir(exist_ok=True)
    
    def teardown_method(self):
        """Cleanup after each test method"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_system_recovery_from_errors(self):
        """Test system recovery from various error conditions"""
        # Create minimal config
        config_data = {
            'models': {
                'models_directory': str(self.models_dir),
                'cache_size': 5,
                'max_memory_usage': 0.5
            },
            'logging': {
                'level': 'INFO',
                'log_to_file': False
            }
        }
        
        config_file = self.config_dir / "config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(config_data, f)
        
        config_manager = ConfigManager(str(self.config_dir))
        
        with patch('core.model_manager.config_manager', config_manager):
            model_manager = ModelManager()
            
            # Test recovery from file not found
            try:
                model_manager.load_model("/nonexistent/model.pkl")
            except ModelError:
                pass  # Expected error
            
            # System should still be functional
            health = model_manager.health_check()
            assert health["status"] == "healthy"
            
            # Test recovery from invalid features
            # First create a valid model
            from sklearn.linear_model import LinearRegression
            model = LinearRegression()
            model.fit([[1], [2]], [1, 2])
            
            model_path = self.models_dir / "recovery_test.pkl"
            joblib.dump(model, model_path)
            
            # Load valid model
            loaded_model = model_manager.load_model(str(model_path))
            assert loaded_model is not None
            
            # Try invalid prediction
            try:
                model_manager.predict(str(model_path), "invalid_features")
            except Exception:
                pass  # Expected error
            
            # System should still work with valid features
            valid_features = np.array([[3]])
            predictions = model_manager.predict(str(model_path), valid_features)
            assert len(predictions) == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
