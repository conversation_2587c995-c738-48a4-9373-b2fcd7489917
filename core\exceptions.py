"""
Enhanced Exception Handling System
Provides comprehensive error handling with context and recovery mechanisms.
"""

import logging
import traceback
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SystemError(Exception):
    """
    Base system exception with enhanced context and recovery information.
    
    Provides structured error handling with:
    - Detailed context information
    - Error severity classification
    - Recovery suggestions
    - Automatic logging
    """
    
    def __init__(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        recovery_suggestions: Optional[List[str]] = None,
        original_exception: Optional[Exception] = None
    ):
        self.message = message
        self.context = context or {}
        self.severity = severity
        self.recovery_suggestions = recovery_suggestions or []
        self.original_exception = original_exception
        self.timestamp = datetime.now()
        self.traceback_info = traceback.format_exc() if original_exception else None
        
        # Auto-log the error
        self._log_error()
        
        super().__init__(self.message)
    
    def _log_error(self):
        """Automatically log the error with appropriate level"""
        logger = logging.getLogger(__name__)
        
        log_message = f"{self.message}"
        if self.context:
            log_message += f" | Context: {self.context}"
        
        if self.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif self.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for serialization"""
        return {
            'message': self.message,
            'context': self.context,
            'severity': self.severity.value,
            'recovery_suggestions': self.recovery_suggestions,
            'timestamp': self.timestamp.isoformat(),
            'traceback': self.traceback_info
        }


class ModelError(SystemError):
    """Model-specific errors (loading, prediction, training)"""
    
    def __init__(self, message: str, model_name: str = None, **kwargs):
        context = kwargs.get('context', {})
        if model_name:
            context['model_name'] = model_name
        kwargs['context'] = context
        super().__init__(message, **kwargs)


class DataError(SystemError):
    """Data processing and validation errors"""
    
    def __init__(self, message: str, data_source: str = None, **kwargs):
        context = kwargs.get('context', {})
        if data_source:
            context['data_source'] = data_source
        kwargs['context'] = context
        super().__init__(message, **kwargs)


class ConfigurationError(SystemError):
    """Configuration and setup errors"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        context = kwargs.get('context', {})
        if config_key:
            context['config_key'] = config_key
        kwargs['context'] = context
        kwargs['severity'] = kwargs.get('severity', ErrorSeverity.HIGH)
        super().__init__(message, **kwargs)


class NetworkError(SystemError):
    """Network and API communication errors"""
    
    def __init__(self, message: str, endpoint: str = None, status_code: int = None, **kwargs):
        context = kwargs.get('context', {})
        if endpoint:
            context['endpoint'] = endpoint
        if status_code:
            context['status_code'] = status_code
        kwargs['context'] = context
        super().__init__(message, **kwargs)


class ValidationError(SystemError):
    """Input validation and constraint errors"""
    
    def __init__(self, message: str, field_name: str = None, expected_type: str = None, **kwargs):
        context = kwargs.get('context', {})
        if field_name:
            context['field_name'] = field_name
        if expected_type:
            context['expected_type'] = expected_type
        kwargs['context'] = context
        super().__init__(message, **kwargs)


def handle_exception(func):
    """
    Decorator for automatic exception handling and conversion to SystemError.
    
    Usage:
        @handle_exception
        def risky_function():
            # code that might raise exceptions
            pass
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except SystemError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Convert other exceptions to SystemError
            raise SystemError(
                message=f"Unexpected error in {func.__name__}: {str(e)}",
                context={'function': func.__name__, 'args': str(args)[:100]},
                severity=ErrorSeverity.HIGH,
                original_exception=e,
                recovery_suggestions=[
                    "Check input parameters",
                    "Verify system configuration",
                    "Check logs for more details"
                ]
            )
    return wrapper


class ErrorRecovery:
    """
    Error recovery and retry mechanisms.
    """
    
    @staticmethod
    def retry_with_backoff(
        func,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exceptions: tuple = (Exception,)
    ):
        """
        Retry function with exponential backoff.
        
        Args:
            func: Function to retry
            max_retries: Maximum number of retry attempts
            base_delay: Base delay between retries (seconds)
            max_delay: Maximum delay between retries (seconds)
            exceptions: Tuple of exceptions to catch and retry
        """
        import time
        import random
        
        for attempt in range(max_retries + 1):
            try:
                return func()
            except exceptions as e:
                if attempt == max_retries:
                    raise SystemError(
                        message=f"Function failed after {max_retries} retries",
                        context={'function': func.__name__, 'last_error': str(e)},
                        severity=ErrorSeverity.HIGH,
                        original_exception=e,
                        recovery_suggestions=[
                            "Check system resources",
                            "Verify network connectivity",
                            "Review function parameters"
                        ]
                    )
                
                # Calculate delay with jitter
                delay = min(base_delay * (2 ** attempt) + random.uniform(0, 1), max_delay)
                time.sleep(delay)
    
    @staticmethod
    def circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
        """
        Circuit breaker pattern for preventing cascade failures.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before attempting recovery
        """
        def decorator(func):
            func._failure_count = 0
            func._last_failure_time = 0
            func._circuit_open = False
            
            def wrapper(*args, **kwargs):
                import time
                
                current_time = time.time()
                
                # Check if circuit should be reset
                if func._circuit_open and (current_time - func._last_failure_time) > recovery_timeout:
                    func._circuit_open = False
                    func._failure_count = 0
                
                # If circuit is open, fail fast
                if func._circuit_open:
                    raise SystemError(
                        message=f"Circuit breaker open for {func.__name__}",
                        context={'failure_count': func._failure_count},
                        severity=ErrorSeverity.HIGH,
                        recovery_suggestions=[
                            f"Wait {recovery_timeout} seconds for circuit to reset",
                            "Check underlying system health",
                            "Review recent error logs"
                        ]
                    )
                
                try:
                    result = func(*args, **kwargs)
                    # Reset failure count on success
                    func._failure_count = 0
                    return result
                except Exception as e:
                    func._failure_count += 1
                    func._last_failure_time = current_time
                    
                    # Open circuit if threshold reached
                    if func._failure_count >= failure_threshold:
                        func._circuit_open = True
                    
                    raise
            
            return wrapper
        return decorator


class HealthChecker:
    """
    System health monitoring and validation.
    """
    
    def __init__(self):
        self.checks = {}
    
    def register_check(self, name: str, check_func, critical: bool = False):
        """Register a health check function"""
        self.checks[name] = {
            'function': check_func,
            'critical': critical
        }
    
    def run_checks(self) -> Dict[str, Any]:
        """Run all registered health checks"""
        results = {
            'overall_status': 'healthy',
            'checks': {},
            'critical_failures': []
        }
        
        for name, check_info in self.checks.items():
            try:
                check_result = check_info['function']()
                results['checks'][name] = {
                    'status': 'pass',
                    'result': check_result
                }
            except Exception as e:
                results['checks'][name] = {
                    'status': 'fail',
                    'error': str(e)
                }
                
                if check_info['critical']:
                    results['critical_failures'].append(name)
                    results['overall_status'] = 'unhealthy'
        
        return results


# Global health checker instance
health_checker = HealthChecker()
