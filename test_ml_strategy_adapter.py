#!/usr/bin/env python
"""
Test ML Strategy Adapter

Tests the ML strategy adapter functionality without requiring trained models.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def test_ml_strategy_adapter():
    """Test ML strategy adapter functionality"""
    
    print("🧪 TESTING ML STRATEGY ADAPTER")
    print("=" * 60)
    
    try:
        from ml_strategy_adapter import MLModelStrategy, MLBacktestRunner
        print("✅ Successfully imported ML strategy adapter")
    except ImportError as e:
        print(f"❌ Failed to import ML strategy adapter: {e}")
        return False
    
    # Test 1: Strategy initialization without models
    print("\n📋 Test 1: Strategy initialization")
    try:
        strategy = MLModelStrategy(model_path="nonexistent_path", confidence_threshold=0.7)
        print("✅ Strategy initialized successfully (no models expected)")
    except Exception as e:
        print(f"❌ Strategy initialization failed: {e}")
        return False
    
    # Test 2: Generate sample data
    print("\n📋 Test 2: Sample data generation")
    try:
        dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
        np.random.seed(42)
        
        # Simulate realistic price data
        returns = np.random.normal(0.0005, 0.02, len(dates))
        prices = 100 * np.exp(np.cumsum(returns))
        
        sample_data = pd.DataFrame({
            'open': prices * (1 + np.random.normal(0, 0.001, len(dates))),
            'high': prices * (1 + abs(np.random.normal(0, 0.005, len(dates)))),
            'low': prices * (1 - abs(np.random.normal(0, 0.005, len(dates)))),
            'close': prices,
            'volume': np.random.lognormal(15, 0.5, len(dates))
        }, index=dates)
        
        print(f"✅ Generated sample data: {sample_data.shape}")
        print(f"   Date range: {sample_data.index.min()} to {sample_data.index.max()}")
        
    except Exception as e:
        print(f"❌ Sample data generation failed: {e}")
        return False
    
    # Test 3: Feature calculation
    print("\n📋 Test 3: Feature calculation")
    try:
        strategy.calculate_indicators(sample_data)
        print(f"✅ Features calculated successfully")
        print(f"   Total columns after features: {len(sample_data.columns)}")
        
        # Check for key features
        key_features = ['returns', 'rsi', 'macd', 'bb_position', 'atr']
        missing_features = [f for f in key_features if f not in sample_data.columns]
        if missing_features:
            print(f"⚠️ Missing features: {missing_features}")
        else:
            print("✅ All key features present")
            
    except Exception as e:
        print(f"❌ Feature calculation failed: {e}")
        return False
    
    # Test 4: Signal generation (without models)
    print("\n📋 Test 4: Signal generation")
    try:
        signals = strategy.generate_signals(sample_data)
        print(f"✅ Signals generated successfully")
        print(f"   Signal shape: {signals.shape}")
        print(f"   Signal distribution: {signals.value_counts().to_dict()}")
        
    except Exception as e:
        print(f"❌ Signal generation failed: {e}")
        return False
    
    # Test 5: Backtest runner initialization
    print("\n📋 Test 5: Backtest runner")
    try:
        runner = MLBacktestRunner(model_path="nonexistent_path")
        print("✅ Backtest runner initialized successfully")
        
    except Exception as e:
        print(f"❌ Backtest runner initialization failed: {e}")
        return False
    
    print("\n🎯 SUMMARY:")
    print("✅ All ML strategy adapter tests passed!")
    print("✅ Framework is ready for integration with trained models")
    print("✅ Backtesting pipeline is functional")
    
    return True

def test_backtesting_framework():
    """Test basic backtesting framework functionality"""
    
    print("\n🧪 TESTING BACKTESTING FRAMEWORK")
    print("=" * 60)
    
    try:
        from backtesting_framework import BacktestConfig, BacktestEngine, Strategy
        print("✅ Successfully imported backtesting framework")
        
        # Test basic configuration
        config = BacktestConfig(
            initial_capital=100000,
            commission=0.001,
            slippage=0.0005
        )
        print("✅ Backtest configuration created")
        
        # Test engine initialization
        engine = BacktestEngine(config)
        print("✅ Backtest engine initialized")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import backtesting framework: {e}")
        return False
    except Exception as e:
        print(f"❌ Backtesting framework test failed: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🚀 NORYONAI SYSTEM INTEGRATION TESTS")
    print("=" * 80)
    
    # Test backtesting framework first
    bt_success = test_backtesting_framework()
    
    # Test ML strategy adapter
    ml_success = test_ml_strategy_adapter()
    
    print("\n" + "=" * 80)
    print("🎯 FINAL TEST RESULTS:")
    print(f"   Backtesting Framework: {'✅ PASS' if bt_success else '❌ FAIL'}")
    print(f"   ML Strategy Adapter: {'✅ PASS' if ml_success else '❌ FAIL'}")
    
    if bt_success and ml_success:
        print("\n🎉 ALL SYSTEMS READY FOR TRAINING!")
        print("🚀 You can now proceed with model training and backtesting")
    else:
        print("\n⚠️ Some systems need attention before training")
        
    return bt_success and ml_success

if __name__ == "__main__":
    main() 