"""
Enhanced Configuration Management System
Provides centralized, validated, and environment-aware configuration management.
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

from core.exceptions import ConfigurationError, ValidationError, handle_exception


class Environment(Enum):
    """Deployment environments"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "noryonai"
    username: str = "noryonai_user"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30


@dataclass
class ModelConfig:
    """Model configuration"""
    models_directory: str = "models"
    cache_size: int = 100
    lazy_loading: bool = True
    prediction_timeout: int = 30
    batch_size: int = 32
    max_memory_usage: float = 2.0  # GB


@dataclass
class TradingConfig:
    """Trading system configuration"""
    initial_capital: float = 100000.0
    max_position_size: float = 0.1
    risk_threshold: float = 0.03
    min_confidence: float = 0.6
    stop_loss: float = 0.02
    take_profit: float = 0.04
    max_daily_trades: int = 50


@dataclass
class DataConfig:
    """Data processing configuration"""
    data_directory: str = "data"
    chunk_size_mb: int = 128
    max_memory_gb: float = 8.0
    max_workers: int = 8
    cache_enabled: bool = True
    cache_expiry: int = 3600


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_to_file: bool = True
    log_file: str = "logs/noryonai.log"
    max_file_size_mb: int = 10
    backup_count: int = 5
    log_rotation: bool = True


@dataclass
class SecurityConfig:
    """Security configuration"""
    secret_key: str = ""
    jwt_expiry: int = 3600
    rate_limit_per_minute: int = 100
    enable_cors: bool = True
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    encryption_enabled: bool = True


@dataclass
class SystemConfig:
    """Complete system configuration"""
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True
    version: str = "2.0.0"
    
    # Component configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    models: ModelConfig = field(default_factory=ModelConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    data: DataConfig = field(default_factory=DataConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    
    # Feature flags
    enable_trading: bool = True
    enable_monitoring: bool = True
    enable_api: bool = True
    enable_web_interface: bool = True


class ConfigManager:
    """
    Enhanced configuration manager with validation and environment support.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config"
        self.config = SystemConfig()
        self._load_configuration()
        self._validate_configuration()
    
    @handle_exception
    def _load_configuration(self):
        """Load configuration from multiple sources"""
        # 1. Load from default config file
        self._load_from_file()
        
        # 2. Override with environment-specific config
        self._load_environment_config()
        
        # 3. Override with environment variables
        self._load_from_environment()
        
        # 4. Load secrets if available
        self._load_secrets()
    
    def _load_from_file(self):
        """Load configuration from YAML/JSON files"""
        config_file = Path(self.config_path) / "config.yaml"
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config_data = yaml.safe_load(f)
                self._update_config_from_dict(config_data)
            except Exception as e:
                raise ConfigurationError(
                    f"Failed to load config file: {config_file}",
                    context={'file': str(config_file), 'error': str(e)},
                    recovery_suggestions=[
                        "Check YAML syntax",
                        "Verify file permissions",
                        "Use default configuration"
                    ]
                )
    
    def _load_environment_config(self):
        """Load environment-specific configuration"""
        env = os.getenv('NORYONAI_ENV', 'development')
        self.config.environment = Environment(env)
        
        env_config_file = Path(self.config_path) / f"config.{env}.yaml"
        if env_config_file.exists():
            try:
                with open(env_config_file, 'r') as f:
                    env_config = yaml.safe_load(f)
                self._update_config_from_dict(env_config)
            except Exception as e:
                raise ConfigurationError(
                    f"Failed to load environment config: {env_config_file}",
                    context={'environment': env, 'file': str(env_config_file)}
                )
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        env_mappings = {
            'NORYONAI_DEBUG': ('debug', bool),
            'NORYONAI_DB_HOST': ('database.host', str),
            'NORYONAI_DB_PORT': ('database.port', int),
            'NORYONAI_DB_PASSWORD': ('database.password', str),
            'NORYONAI_SECRET_KEY': ('security.secret_key', str),
            'NORYONAI_LOG_LEVEL': ('logging.level', str),
            'NORYONAI_INITIAL_CAPITAL': ('trading.initial_capital', float),
            'NORYONAI_MAX_WORKERS': ('data.max_workers', int),
        }
        
        for env_var, (config_path, config_type) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    typed_value = config_type(value) if config_type != bool else value.lower() in ('true', '1', 'yes')
                    self._set_nested_config(config_path, typed_value)
                except ValueError as e:
                    raise ConfigurationError(
                        f"Invalid environment variable value: {env_var}={value}",
                        context={'variable': env_var, 'expected_type': config_type.__name__}
                    )
    
    def _load_secrets(self):
        """Load secrets from secure storage"""
        secrets_file = Path(self.config_path) / "secrets.yaml"
        if secrets_file.exists():
            try:
                with open(secrets_file, 'r') as f:
                    secrets = yaml.safe_load(f)
                
                # Only load security-related secrets
                if 'security' in secrets:
                    for key, value in secrets['security'].items():
                        setattr(self.config.security, key, value)
                        
            except Exception as e:
                # Don't fail if secrets can't be loaded, but log warning
                import logging
                logging.warning(f"Could not load secrets: {e}")
    
    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary"""
        for section, values in config_dict.items():
            if hasattr(self.config, section) and isinstance(values, dict):
                section_config = getattr(self.config, section)
                for key, value in values.items():
                    if hasattr(section_config, key):
                        setattr(section_config, key, value)
    
    def _set_nested_config(self, path: str, value: Any):
        """Set nested configuration value using dot notation"""
        parts = path.split('.')
        config_obj = self.config
        
        for part in parts[:-1]:
            config_obj = getattr(config_obj, part)
        
        setattr(config_obj, parts[-1], value)
    
    @handle_exception
    def _validate_configuration(self):
        """Validate configuration values"""
        validators = [
            self._validate_database_config,
            self._validate_model_config,
            self._validate_trading_config,
            self._validate_security_config
        ]
        
        for validator in validators:
            validator()
    
    def _validate_database_config(self):
        """Validate database configuration"""
        db_config = self.config.database
        
        if not db_config.host:
            raise ValidationError("Database host cannot be empty", field_name="database.host")
        
        if not (1 <= db_config.port <= 65535):
            raise ValidationError(
                f"Database port must be between 1-65535, got {db_config.port}",
                field_name="database.port"
            )
        
        if db_config.pool_size <= 0:
            raise ValidationError("Database pool size must be positive", field_name="database.pool_size")
    
    def _validate_model_config(self):
        """Validate model configuration"""
        model_config = self.config.models
        
        if not Path(model_config.models_directory).exists():
            raise ValidationError(
                f"Models directory does not exist: {model_config.models_directory}",
                field_name="models.models_directory",
                recovery_suggestions=["Create the models directory", "Check path configuration"]
            )
        
        if model_config.max_memory_usage <= 0:
            raise ValidationError("Max memory usage must be positive", field_name="models.max_memory_usage")
    
    def _validate_trading_config(self):
        """Validate trading configuration"""
        trading_config = self.config.trading
        
        if trading_config.initial_capital <= 0:
            raise ValidationError("Initial capital must be positive", field_name="trading.initial_capital")
        
        if not (0 < trading_config.max_position_size <= 1):
            raise ValidationError(
                "Max position size must be between 0 and 1",
                field_name="trading.max_position_size"
            )
        
        if not (0 < trading_config.min_confidence <= 1):
            raise ValidationError(
                "Min confidence must be between 0 and 1",
                field_name="trading.min_confidence"
            )
    
    def _validate_security_config(self):
        """Validate security configuration"""
        security_config = self.config.security
        
        if self.config.environment == Environment.PRODUCTION:
            if not security_config.secret_key:
                raise ValidationError(
                    "Secret key is required in production",
                    field_name="security.secret_key",
                    recovery_suggestions=["Set NORYONAI_SECRET_KEY environment variable"]
                )
            
            if len(security_config.secret_key) < 32:
                raise ValidationError(
                    "Secret key must be at least 32 characters in production",
                    field_name="security.secret_key"
                )
    
    def get_config(self) -> SystemConfig:
        """Get the complete configuration"""
        return self.config
    
    def get_section(self, section: str) -> Any:
        """Get a specific configuration section"""
        if not hasattr(self.config, section):
            raise ConfigurationError(f"Configuration section '{section}' not found")
        return getattr(self.config, section)
    
    def save_config(self, file_path: Optional[str] = None):
        """Save current configuration to file"""
        if file_path is None:
            file_path = Path(self.config_path) / "config.yaml"
        
        config_dict = self._config_to_dict()
        
        try:
            with open(file_path, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
        except Exception as e:
            raise ConfigurationError(
                f"Failed to save configuration to {file_path}",
                context={'file': str(file_path), 'error': str(e)}
            )
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        import dataclasses
        
        def convert_dataclass(obj):
            if dataclasses.is_dataclass(obj):
                return {k: convert_dataclass(v) for k, v in dataclasses.asdict(obj).items()}
            elif isinstance(obj, Enum):
                return obj.value
            return obj
        
        return convert_dataclass(self.config)


# Global configuration manager instance
config_manager = ConfigManager()
