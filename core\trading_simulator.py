"""
Advanced Trading Simulation System
Simulates trading strategies using AI models with realistic market conditions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from core.exceptions import SystemError, ValidationError, handle_exception
from core.config_manager import config_manager
from core.logging_system import get_logger, get_performance_logger
from core.model_manager import model_manager
from core.performance_monitor import performance_monitor


class OrderType(Enum):
    """Trading order types"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order execution status"""
    PENDING = "pending"
    EXECUTED = "executed"
    CANCELLED = "cancelled"


@dataclass
class Trade:
    """Individual trade record"""
    id: str
    timestamp: datetime
    symbol: str
    order_type: OrderType
    entry_price: float
    exit_price: Optional[float] = None
    quantity: float = 1.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    model_confidence: float = 0.0
    pnl: float = 0.0
    duration_minutes: int = 0
    exit_reason: str = ""


@dataclass
class Portfolio:
    """Trading portfolio state"""
    initial_capital: float
    current_capital: float
    positions: Dict[str, float] = field(default_factory=dict)
    open_trades: List[Trade] = field(default_factory=list)
    closed_trades: List[Trade] = field(default_factory=list)
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    peak_capital: float = 0.0


@dataclass
class MarketData:
    """Market data point"""
    timestamp: datetime
    symbol: str
    open: float
    high: float
    low: float
    close: float
    volume: float
    features: Dict[str, float] = field(default_factory=dict)


class MarketDataGenerator:
    """Generates realistic market data for simulation"""
    
    def __init__(self, volatility: float = 0.02, trend: float = 0.0):
        self.volatility = volatility
        self.trend = trend
        self.logger = get_logger(__name__)
    
    def generate_price_series(self, start_price: float = 1.0, 
                            periods: int = 1000, 
                            frequency: str = '1min') -> pd.DataFrame:
        """Generate realistic price series using geometric Brownian motion"""
        np.random.seed(42)  # For reproducible simulations
        
        # Generate random returns
        dt = 1 / (365 * 24 * 60)  # 1 minute in years
        returns = np.random.normal(
            self.trend * dt, 
            self.volatility * np.sqrt(dt), 
            periods
        )
        
        # Generate price series
        prices = [start_price]
        for i in range(periods - 1):
            next_price = prices[-1] * (1 + returns[i])
            prices.append(max(next_price, 0.01))  # Prevent negative prices
        
        # Generate OHLC data
        data = []
        base_time = datetime.now() - timedelta(minutes=periods)
        
        for i, price in enumerate(prices):
            # Add some intraday volatility
            high = price * (1 + abs(np.random.normal(0, self.volatility / 4)))
            low = price * (1 - abs(np.random.normal(0, self.volatility / 4)))
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            
            # Ensure OHLC consistency
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)
            
            volume = np.random.uniform(1000, 10000)
            
            data.append({
                'timestamp': base_time + timedelta(minutes=i),
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        
        # Add technical indicators
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        df['volatility'] = df['returns'].rolling(20).std()
        df['rsi'] = self._calculate_rsi(df['close'])
        df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
        
        return df.fillna(0)
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """Calculate MACD indicator"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal


class TradingStrategy:
    """Base trading strategy using AI models"""
    
    def __init__(self, model_path: str, config: Dict[str, Any] = None):
        self.model_path = model_path
        self.config = config or {}
        self.logger = get_logger(__name__)
        self.model = None
        
        # Strategy parameters
        self.min_confidence = self.config.get('min_confidence', 0.6)
        self.stop_loss_pct = self.config.get('stop_loss', 0.02)
        self.take_profit_pct = self.config.get('take_profit', 0.04)
        self.max_position_size = self.config.get('max_position_size', 0.1)
    
    @handle_exception
    def initialize(self):
        """Initialize the trading strategy"""
        self.model = model_manager.load_model(self.model_path)
        self.logger.info(f"Trading strategy initialized with model: {self.model_path}")
    
    def generate_signal(self, market_data: pd.DataFrame) -> Tuple[OrderType, float]:
        """Generate trading signal from market data"""
        if self.model is None:
            self.initialize()
        
        try:
            # Prepare features for model
            features = self._prepare_features(market_data)
            
            # Get model prediction
            with performance_monitor.track_operation("trading_signal_generation"):
                if hasattr(self.model, 'predict_proba'):
                    probabilities = self.model.predict_proba([features])[0]
                    prediction = np.argmax(probabilities)
                    confidence = np.max(probabilities)
                else:
                    prediction = self.model.predict([features])[0]
                    confidence = 0.8  # Default confidence for models without probability
            
            # Convert prediction to trading signal
            if confidence >= self.min_confidence:
                if prediction == 1:  # Buy signal
                    return OrderType.BUY, confidence
                elif prediction == 0:  # Sell signal
                    return OrderType.SELL, confidence
            
            # No signal if confidence is too low
            return None, confidence
            
        except Exception as e:
            self.logger.error(f"Signal generation failed: {e}")
            return None, 0.0
    
    def _prepare_features(self, market_data: pd.DataFrame) -> List[float]:
        """Prepare features for model prediction"""
        latest = market_data.iloc[-1]
        
        # Basic OHLC features
        features = [
            latest['open'],
            latest['high'],
            latest['low'],
            latest['close'],
            latest['volume'],
            latest.get('returns', 0),
            latest.get('sma_20', latest['close']),
            latest.get('sma_50', latest['close']),
            latest.get('volatility', 0),
            latest.get('rsi', 50),
            latest.get('macd', 0),
            latest.get('macd_signal', 0)
        ]
        
        return features


class TradingSimulator:
    """
    Advanced trading simulator that tests AI models in realistic market conditions.
    """
    
    def __init__(self):
        self.config = config_manager.get_section('trading')
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # Simulation parameters
        self.initial_capital = self.config.initial_capital
        self.commission = 0.001  # 0.1% commission
        self.slippage = 0.0005   # 0.05% slippage
        
        # Market data generator
        self.market_generator = MarketDataGenerator()
        
        # Results storage
        self.simulation_results: List[Dict] = []
    
    @handle_exception
    def run_simulation(self, model_path: str, 
                      simulation_days: int = 30,
                      symbol: str = "EURUSD") -> Dict[str, Any]:
        """Run a complete trading simulation"""
        self.logger.info(f"Starting trading simulation: {model_path}")
        
        # Generate market data
        periods = simulation_days * 24 * 60  # 1-minute bars
        market_data = self.market_generator.generate_price_series(
            start_price=1.1000,  # EUR/USD starting price
            periods=periods
        )
        
        # Initialize strategy
        strategy = TradingStrategy(model_path, {
            'min_confidence': self.config.min_confidence,
            'stop_loss': self.config.stop_loss,
            'take_profit': self.config.take_profit,
            'max_position_size': self.config.max_position_size
        })
        strategy.initialize()
        
        # Initialize portfolio
        portfolio = Portfolio(
            initial_capital=self.initial_capital,
            current_capital=self.initial_capital,
            peak_capital=self.initial_capital
        )
        
        # Run simulation
        with self.performance_logger.timer("trading_simulation", model=model_path):
            portfolio = self._execute_simulation(strategy, market_data, portfolio, symbol)
        
        # Calculate performance metrics
        results = self._calculate_performance_metrics(portfolio, simulation_days)
        
        # Store results
        self.simulation_results.append(results)
        
        self.logger.info(
            f"Simulation completed: {results['total_trades']} trades, "
            f"{results['total_return']:.2%} return, "
            f"{results['win_rate']:.2%} win rate"
        )
        
        return results
    
    def _execute_simulation(self, strategy: TradingStrategy, 
                          market_data: pd.DataFrame, 
                          portfolio: Portfolio, 
                          symbol: str) -> Portfolio:
        """Execute the trading simulation"""
        lookback_period = 50  # Minimum data points for signal generation
        
        for i in range(lookback_period, len(market_data)):
            current_time = market_data.iloc[i]['timestamp']
            current_price = market_data.iloc[i]['close']
            
            # Update portfolio value
            portfolio.current_capital = self._calculate_portfolio_value(portfolio, current_price)
            portfolio.peak_capital = max(portfolio.peak_capital, portfolio.current_capital)
            
            # Check for exit conditions on open trades
            self._check_exit_conditions(portfolio, market_data.iloc[i])
            
            # Generate new signal if no open position
            if not portfolio.open_trades:
                historical_data = market_data.iloc[i-lookback_period:i+1]
                signal, confidence = strategy.generate_signal(historical_data)
                
                if signal is not None:
                    # Execute trade
                    trade = self._execute_trade(
                        signal, current_price, current_time, symbol, confidence, portfolio
                    )
                    if trade:
                        portfolio.open_trades.append(trade)
        
        # Close any remaining open trades
        if portfolio.open_trades:
            final_price = market_data.iloc[-1]['close']
            final_time = market_data.iloc[-1]['timestamp']
            for trade in portfolio.open_trades[:]:
                self._close_trade(trade, final_price, final_time, "simulation_end")
                portfolio.closed_trades.append(trade)
                portfolio.open_trades.remove(trade)
        
        return portfolio
    
    def _execute_trade(self, signal: OrderType, price: float, timestamp: datetime,
                      symbol: str, confidence: float, portfolio: Portfolio) -> Optional[Trade]:
        """Execute a trading order"""
        # Calculate position size
        position_value = portfolio.current_capital * self.config.max_position_size
        quantity = position_value / price
        
        # Apply slippage and commission
        execution_price = price * (1 + self.slippage if signal == OrderType.BUY else 1 - self.slippage)
        commission_cost = position_value * self.commission
        
        # Check if we have enough capital
        total_cost = position_value + commission_cost
        if total_cost > portfolio.current_capital:
            return None
        
        # Create trade
        trade = Trade(
            id=f"{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            timestamp=timestamp,
            symbol=symbol,
            order_type=signal,
            entry_price=execution_price,
            quantity=quantity,
            model_confidence=confidence,
            status=OrderStatus.EXECUTED,
            stop_loss=execution_price * (1 - self.config.stop_loss) if signal == OrderType.BUY 
                     else execution_price * (1 + self.config.stop_loss),
            take_profit=execution_price * (1 + self.config.take_profit) if signal == OrderType.BUY 
                       else execution_price * (1 - self.config.take_profit)
        )
        
        # Update portfolio
        portfolio.current_capital -= total_cost
        
        return trade
    
    def _check_exit_conditions(self, portfolio: Portfolio, market_bar: pd.Series):
        """Check exit conditions for open trades"""
        current_price = market_bar['close']
        current_time = market_bar['timestamp']
        
        for trade in portfolio.open_trades[:]:
            exit_reason = None
            
            if trade.order_type == OrderType.BUY:
                # Check stop loss
                if current_price <= trade.stop_loss:
                    exit_reason = "stop_loss"
                # Check take profit
                elif current_price >= trade.take_profit:
                    exit_reason = "take_profit"
            else:  # SELL
                # Check stop loss
                if current_price >= trade.stop_loss:
                    exit_reason = "stop_loss"
                # Check take profit
                elif current_price <= trade.take_profit:
                    exit_reason = "take_profit"
            
            # Check maximum holding time (24 hours)
            if (current_time - trade.timestamp).total_seconds() > 24 * 3600:
                exit_reason = "max_time"
            
            if exit_reason:
                self._close_trade(trade, current_price, current_time, exit_reason)
                portfolio.closed_trades.append(trade)
                portfolio.open_trades.remove(trade)
    
    def _close_trade(self, trade: Trade, exit_price: float, exit_time: datetime, reason: str):
        """Close a trade and calculate P&L"""
        # Apply slippage
        execution_price = exit_price * (1 - self.slippage if trade.order_type == OrderType.BUY else 1 + self.slippage)
        
        # Calculate P&L
        if trade.order_type == OrderType.BUY:
            pnl = (execution_price - trade.entry_price) * trade.quantity
        else:
            pnl = (trade.entry_price - execution_price) * trade.quantity
        
        # Apply commission
        commission = (trade.entry_price * trade.quantity) * self.commission
        pnl -= commission
        
        # Update trade
        trade.exit_price = execution_price
        trade.pnl = pnl
        trade.exit_reason = reason
        trade.duration_minutes = int((exit_time - trade.timestamp).total_seconds() / 60)
        trade.status = OrderStatus.EXECUTED
    
    def _calculate_portfolio_value(self, portfolio: Portfolio, current_price: float) -> float:
        """Calculate current portfolio value"""
        value = portfolio.current_capital
        
        # Add unrealized P&L from open trades
        for trade in portfolio.open_trades:
            if trade.order_type == OrderType.BUY:
                unrealized_pnl = (current_price - trade.entry_price) * trade.quantity
            else:
                unrealized_pnl = (trade.entry_price - current_price) * trade.quantity
            value += unrealized_pnl
        
        return value
    
    def _calculate_performance_metrics(self, portfolio: Portfolio, simulation_days: int) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        closed_trades = portfolio.closed_trades
        
        if not closed_trades:
            return {
                'model_name': 'unknown',
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'total_return': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'win_rate': 0.0,
                'avg_trade_duration': 0.0,
                'profit_factor': 0.0,
                'simulation_days': simulation_days
            }
        
        # Basic metrics
        total_trades = len(closed_trades)
        winning_trades = sum(1 for t in closed_trades if t.pnl > 0)
        losing_trades = total_trades - winning_trades
        
        total_pnl = sum(t.pnl for t in closed_trades)
        total_return = total_pnl / portfolio.initial_capital
        
        # Win rate
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Average trade duration
        avg_duration = np.mean([t.duration_minutes for t in closed_trades])
        
        # Profit factor
        gross_profit = sum(t.pnl for t in closed_trades if t.pnl > 0)
        gross_loss = abs(sum(t.pnl for t in closed_trades if t.pnl < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Maximum drawdown
        max_drawdown = (portfolio.peak_capital - portfolio.current_capital) / portfolio.peak_capital
        
        # Sharpe ratio (simplified)
        returns = [t.pnl / portfolio.initial_capital for t in closed_trades]
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        return {
            'model_name': 'simulation_model',
            'simulation_period': f"{simulation_days} days",
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'avg_trade_duration': avg_duration,
            'profit_factor': profit_factor,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'final_capital': portfolio.current_capital,
            'timestamp': datetime.now().isoformat(),
            'trade_details': [
                {
                    'id': t.id,
                    'timestamp': t.timestamp.isoformat(),
                    'type': t.order_type.value,
                    'entry_price': t.entry_price,
                    'exit_price': t.exit_price,
                    'pnl': t.pnl,
                    'duration_minutes': t.duration_minutes,
                    'exit_reason': t.exit_reason,
                    'confidence': t.model_confidence
                }
                for t in closed_trades
            ]
        }
    
    def run_batch_simulations(self, model_paths: List[str], 
                            simulation_days: int = 30) -> Dict[str, Any]:
        """Run simulations for multiple models"""
        self.logger.info(f"Running batch simulations for {len(model_paths)} models")
        
        results = []
        
        for i, model_path in enumerate(model_paths):
            try:
                self.logger.info(f"Simulating model {i+1}/{len(model_paths)}: {model_path}")
                result = self.run_simulation(model_path, simulation_days)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Simulation failed for {model_path}: {e}")
                results.append({
                    'model_name': model_path,
                    'error': str(e),
                    'total_trades': 0,
                    'total_return': 0.0
                })
        
        # Generate summary
        successful_sims = [r for r in results if 'error' not in r]
        
        summary = {
            'total_simulations': len(results),
            'successful_simulations': len(successful_sims),
            'failed_simulations': len(results) - len(successful_sims),
            'avg_return': np.mean([r['total_return'] for r in successful_sims]) if successful_sims else 0,
            'best_model': max(successful_sims, key=lambda x: x['total_return']) if successful_sims else None,
            'worst_model': min(successful_sims, key=lambda x: x['total_return']) if successful_sims else None,
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
        
        return summary
    
    def save_simulation_results(self, filename: Optional[str] = None):
        """Save simulation results to file"""
        if filename is None:
            filename = f"trading_simulation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.simulation_results, f, indent=2)
        
        self.logger.info(f"Simulation results saved to: {filename}")


# Global trading simulator instance
trading_simulator = TradingSimulator()
