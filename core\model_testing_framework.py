"""
Comprehensive AI Model Testing and Simulation Framework
Provides model validation, performance testing, and simulation capabilities.
"""

import numpy as np
import pandas as pd
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import json
import concurrent.futures
from abc import ABC, abstractmethod

from core.exceptions import ModelError, ValidationError, handle_exception
from core.config_manager import config_manager
from core.logging_system import get_logger, get_performance_logger
from core.model_manager import model_manager
from core.performance_monitor import performance_monitor


@dataclass
class ModelTestResult:
    """Model test result with comprehensive metrics"""
    model_name: str
    test_type: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    execution_time: float
    memory_usage_mb: float
    predictions_per_second: float
    error_rate: float
    confidence_scores: List[float]
    test_data_size: int
    timestamp: datetime
    passed: bool
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SimulationResult:
    """Trading simulation result"""
    model_name: str
    simulation_period: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    avg_trade_duration: float
    profit_factor: float
    timestamp: datetime
    trade_details: List[Dict] = field(default_factory=list)


class ModelTester(ABC):
    """Abstract base class for model testing"""
    
    @abstractmethod
    def test_model(self, model_path: str, test_data: pd.DataFrame) -> ModelTestResult:
        """Test a model and return results"""
        pass
    
    @abstractmethod
    def validate_model_format(self, model_path: str) -> bool:
        """Validate model file format"""
        pass


class ClassificationModelTester(ModelTester):
    """Tester for classification models"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
    
    def test_model(self, model_path: str, test_data: pd.DataFrame) -> ModelTestResult:
        """Test classification model performance"""
        start_time = time.time()
        
        try:
            # Load model
            model = model_manager.load_model(model_path)
            
            # Prepare test data
            X_test = test_data.drop('target', axis=1) if 'target' in test_data.columns else test_data
            y_test = test_data['target'] if 'target' in test_data.columns else None
            
            # Make predictions
            with performance_monitor.track_operation("model_prediction_test"):
                predictions = model.predict(X_test)
                if hasattr(model, 'predict_proba'):
                    probabilities = model.predict_proba(X_test)
                    confidence_scores = np.max(probabilities, axis=1).tolist()
                else:
                    confidence_scores = [1.0] * len(predictions)
            
            execution_time = time.time() - start_time
            predictions_per_second = len(predictions) / execution_time if execution_time > 0 else 0
            
            # Calculate metrics if we have ground truth
            if y_test is not None:
                from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
                
                accuracy = accuracy_score(y_test, predictions)
                precision = precision_score(y_test, predictions, average='weighted', zero_division=0)
                recall = recall_score(y_test, predictions, average='weighted', zero_division=0)
                f1 = f1_score(y_test, predictions, average='weighted', zero_division=0)
                error_rate = 1 - accuracy
            else:
                # No ground truth available
                accuracy = precision = recall = f1 = 0.0
                error_rate = 0.0
            
            # Memory usage estimation
            import psutil
            process = psutil.Process()
            memory_usage_mb = process.memory_info().rss / (1024 * 1024)
            
            # Determine if test passed
            passed = accuracy >= 0.7 and error_rate <= 0.3  # Configurable thresholds
            
            return ModelTestResult(
                model_name=Path(model_path).stem,
                test_type="classification",
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1,
                execution_time=execution_time,
                memory_usage_mb=memory_usage_mb,
                predictions_per_second=predictions_per_second,
                error_rate=error_rate,
                confidence_scores=confidence_scores,
                test_data_size=len(X_test),
                timestamp=datetime.now(),
                passed=passed,
                details={
                    'unique_predictions': len(np.unique(predictions)),
                    'avg_confidence': np.mean(confidence_scores),
                    'min_confidence': np.min(confidence_scores),
                    'max_confidence': np.max(confidence_scores)
                }
            )
            
        except Exception as e:
            raise ModelError(
                f"Model testing failed: {e}",
                context={'model_path': model_path, 'test_data_shape': test_data.shape},
                recovery_suggestions=[
                    "Check model file integrity",
                    "Verify test data format",
                    "Check feature compatibility"
                ]
            )
    
    def validate_model_format(self, model_path: str) -> bool:
        """Validate classification model format"""
        try:
            model = model_manager.load_model(model_path)
            return hasattr(model, 'predict')
        except Exception:
            return False


class RegressionModelTester(ModelTester):
    """Tester for regression models"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
    
    def test_model(self, model_path: str, test_data: pd.DataFrame) -> ModelTestResult:
        """Test regression model performance"""
        start_time = time.time()
        
        try:
            # Load model
            model = model_manager.load_model(model_path)
            
            # Prepare test data
            X_test = test_data.drop('target', axis=1) if 'target' in test_data.columns else test_data
            y_test = test_data['target'] if 'target' in test_data.columns else None
            
            # Make predictions
            with performance_monitor.track_operation("model_prediction_test"):
                predictions = model.predict(X_test)
                confidence_scores = [1.0] * len(predictions)  # Regression doesn't have confidence
            
            execution_time = time.time() - start_time
            predictions_per_second = len(predictions) / execution_time if execution_time > 0 else 0
            
            # Calculate metrics if we have ground truth
            if y_test is not None:
                from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
                
                mse = mean_squared_error(y_test, predictions)
                mae = mean_absolute_error(y_test, predictions)
                r2 = r2_score(y_test, predictions)
                
                # Convert to classification-like metrics for consistency
                accuracy = max(0, r2)  # R² as accuracy proxy
                precision = recall = f1_score = accuracy
                error_rate = 1 - accuracy if accuracy > 0 else 1.0
            else:
                accuracy = precision = recall = f1_score = 0.0
                error_rate = 0.0
                mse = mae = r2 = 0.0
            
            # Memory usage estimation
            import psutil
            process = psutil.Process()
            memory_usage_mb = process.memory_info().rss / (1024 * 1024)
            
            # Determine if test passed
            passed = accuracy >= 0.6 and error_rate <= 0.4  # Configurable thresholds
            
            return ModelTestResult(
                model_name=Path(model_path).stem,
                test_type="regression",
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1_score,
                execution_time=execution_time,
                memory_usage_mb=memory_usage_mb,
                predictions_per_second=predictions_per_second,
                error_rate=error_rate,
                confidence_scores=confidence_scores,
                test_data_size=len(X_test),
                timestamp=datetime.now(),
                passed=passed,
                details={
                    'mse': mse,
                    'mae': mae,
                    'r2_score': r2,
                    'prediction_range': [float(np.min(predictions)), float(np.max(predictions))],
                    'prediction_std': float(np.std(predictions))
                }
            )
            
        except Exception as e:
            raise ModelError(
                f"Regression model testing failed: {e}",
                context={'model_path': model_path, 'test_data_shape': test_data.shape}
            )
    
    def validate_model_format(self, model_path: str) -> bool:
        """Validate regression model format"""
        try:
            model = model_manager.load_model(model_path)
            return hasattr(model, 'predict')
        except Exception:
            return False


class ModelTestingFramework:
    """
    Comprehensive model testing framework that coordinates all testing activities.
    """
    
    def __init__(self):
        self.config = config_manager.get_config()
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # Initialize testers
        self.testers = {
            'classification': ClassificationModelTester(),
            'regression': RegressionModelTester()
        }
        
        # Test results storage
        self.test_results: List[ModelTestResult] = []
        
        # Models directory
        self.models_dir = Path(self.config.models.models_directory)
    
    @handle_exception
    def discover_models(self) -> Dict[str, List[str]]:
        """Discover all available models by category"""
        models = {
            'financial': [],
            'reasoning': [],
            'classification': [],
            'regression': [],
            'other': []
        }
        
        if not self.models_dir.exists():
            self.logger.warning(f"Models directory not found: {self.models_dir}")
            return models
        
        # Scan for model files
        for model_file in self.models_dir.rglob("*.pkl"):
            category = self._categorize_model(model_file)
            models[category].append(str(model_file))
        
        self.logger.info(f"Discovered models: {sum(len(v) for v in models.values())} total")
        return models
    
    def _categorize_model(self, model_path: Path) -> str:
        """Categorize model based on path and name"""
        path_str = str(model_path).lower()
        
        if 'financial' in path_str or 'trading' in path_str or 'forex' in path_str:
            return 'financial'
        elif 'reasoning' in path_str or 'logic' in path_str:
            return 'reasoning'
        elif 'classification' in path_str or 'classifier' in path_str:
            return 'classification'
        elif 'regression' in path_str or 'regressor' in path_str:
            return 'regression'
        else:
            return 'other'
    
    @handle_exception
    def test_model(self, model_path: str, test_data: pd.DataFrame, 
                   model_type: str = 'classification') -> ModelTestResult:
        """Test a single model"""
        self.logger.info(f"Testing model: {model_path}")
        
        if model_type not in self.testers:
            raise ValidationError(f"Unsupported model type: {model_type}")
        
        tester = self.testers[model_type]
        
        # Validate model format first
        if not tester.validate_model_format(model_path):
            raise ModelError(f"Invalid model format: {model_path}")
        
        # Run the test
        with self.performance_logger.timer("model_test", model=Path(model_path).stem):
            result = tester.test_model(model_path, test_data)
        
        # Store result
        self.test_results.append(result)
        
        # Log result
        status = "PASSED" if result.passed else "FAILED"
        self.logger.info(
            f"Model test {status}: {result.model_name} - "
            f"Accuracy: {result.accuracy:.3f}, "
            f"Speed: {result.predictions_per_second:.1f} pred/sec"
        )
        
        return result
    
    @handle_exception
    def test_all_models(self, test_data_generator: Callable = None) -> Dict[str, List[ModelTestResult]]:
        """Test all discovered models"""
        self.logger.info("Starting comprehensive model testing")
        
        models = self.discover_models()
        results = {category: [] for category in models.keys()}
        
        # Generate test data if not provided
        if test_data_generator is None:
            test_data_generator = self._generate_synthetic_test_data
        
        total_models = sum(len(model_list) for model_list in models.values())
        tested_models = 0
        
        for category, model_list in models.items():
            if not model_list:
                continue
                
            self.logger.info(f"Testing {len(model_list)} {category} models")
            
            for model_path in model_list:
                try:
                    # Generate appropriate test data
                    test_data = test_data_generator(category)
                    
                    # Determine model type for testing
                    model_type = 'regression' if category == 'regression' else 'classification'
                    
                    # Test the model
                    result = self.test_model(model_path, test_data, model_type)
                    results[category].append(result)
                    
                    tested_models += 1
                    progress = (tested_models / total_models) * 100
                    self.logger.info(f"Progress: {progress:.1f}% ({tested_models}/{total_models})")
                    
                except Exception as e:
                    self.logger.error(f"Failed to test model {model_path}: {e}")
                    # Create failed result
                    failed_result = ModelTestResult(
                        model_name=Path(model_path).stem,
                        test_type=category,
                        accuracy=0.0,
                        precision=0.0,
                        recall=0.0,
                        f1_score=0.0,
                        execution_time=0.0,
                        memory_usage_mb=0.0,
                        predictions_per_second=0.0,
                        error_rate=1.0,
                        confidence_scores=[],
                        test_data_size=0,
                        timestamp=datetime.now(),
                        passed=False,
                        details={'error': str(e)}
                    )
                    results[category].append(failed_result)
        
        self.logger.info(f"Model testing completed: {tested_models} models tested")
        return results
    
    def _generate_synthetic_test_data(self, category: str) -> pd.DataFrame:
        """Generate synthetic test data for model testing"""
        np.random.seed(42)  # For reproducible results
        
        if category == 'financial':
            # Financial data simulation
            n_samples = 1000
            data = {
                'open': np.random.uniform(1.0, 2.0, n_samples),
                'high': np.random.uniform(1.0, 2.0, n_samples),
                'low': np.random.uniform(1.0, 2.0, n_samples),
                'close': np.random.uniform(1.0, 2.0, n_samples),
                'volume': np.random.uniform(1000, 10000, n_samples),
                'rsi': np.random.uniform(0, 100, n_samples),
                'macd': np.random.uniform(-1, 1, n_samples),
                'target': np.random.randint(0, 2, n_samples)  # Buy/Sell signals
            }
        elif category == 'regression':
            # Regression test data
            n_samples = 500
            X = np.random.randn(n_samples, 5)
            y = X[:, 0] * 2 + X[:, 1] * 1.5 + np.random.randn(n_samples) * 0.1
            
            data = {f'feature_{i}': X[:, i] for i in range(5)}
            data['target'] = y
        else:
            # Generic classification data
            n_samples = 800
            n_features = 10
            X = np.random.randn(n_samples, n_features)
            y = np.random.randint(0, 3, n_samples)  # 3-class classification
            
            data = {f'feature_{i}': X[:, i] for i in range(n_features)}
            data['target'] = y
        
        return pd.DataFrame(data)
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        if not self.test_results:
            return {'error': 'No test results available'}
        
        # Calculate summary statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.passed)
        failed_tests = total_tests - passed_tests
        
        avg_accuracy = np.mean([r.accuracy for r in self.test_results])
        avg_speed = np.mean([r.predictions_per_second for r in self.test_results])
        avg_memory = np.mean([r.memory_usage_mb for r in self.test_results])
        
        # Group by test type
        by_type = {}
        for result in self.test_results:
            if result.test_type not in by_type:
                by_type[result.test_type] = []
            by_type[result.test_type].append(result)
        
        type_summaries = {}
        for test_type, results in by_type.items():
            type_summaries[test_type] = {
                'total': len(results),
                'passed': sum(1 for r in results if r.passed),
                'avg_accuracy': np.mean([r.accuracy for r in results]),
                'avg_speed': np.mean([r.predictions_per_second for r in results])
            }
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
                'avg_accuracy': avg_accuracy,
                'avg_predictions_per_second': avg_speed,
                'avg_memory_usage_mb': avg_memory
            },
            'by_type': type_summaries,
            'top_performers': sorted(
                [r for r in self.test_results if r.passed],
                key=lambda x: x.accuracy,
                reverse=True
            )[:5],
            'failed_models': [r for r in self.test_results if not r.passed],
            'timestamp': datetime.now().isoformat()
        }
    
    def save_test_results(self, filename: Optional[str] = None):
        """Save test results to file"""
        if filename is None:
            filename = f"model_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = self.generate_test_report()
        
        # Convert results to serializable format
        serializable_results = []
        for result in self.test_results:
            result_dict = {
                'model_name': result.model_name,
                'test_type': result.test_type,
                'accuracy': result.accuracy,
                'precision': result.precision,
                'recall': result.recall,
                'f1_score': result.f1_score,
                'execution_time': result.execution_time,
                'memory_usage_mb': result.memory_usage_mb,
                'predictions_per_second': result.predictions_per_second,
                'error_rate': result.error_rate,
                'test_data_size': result.test_data_size,
                'timestamp': result.timestamp.isoformat(),
                'passed': result.passed,
                'details': result.details
            }
            serializable_results.append(result_dict)
        
        report['detailed_results'] = serializable_results
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Test results saved to: {filename}")


# Global model testing framework instance
model_testing_framework = ModelTestingFramework()
