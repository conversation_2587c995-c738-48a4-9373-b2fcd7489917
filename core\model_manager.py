"""
Enhanced Model Management System
Provides intelligent model loading, caching, and lifecycle management.
"""

import joblib
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Union, Tuple
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass
from threading import Lock
import weakref
import gc
import psutil
import hashlib

from core.exceptions import ModelError, ValidationError, handle_exception
from core.config_manager import config_manager
from core.logging_system import get_logger, get_performance_logger


@dataclass
class ModelMetadata:
    """Model metadata and information"""
    name: str
    path: str
    model_type: str
    version: str
    created_at: datetime
    last_used: datetime
    accuracy: Optional[float] = None
    size_mb: Optional[float] = None
    feature_count: Optional[int] = None
    target_type: Optional[str] = None
    training_data_hash: Optional[str] = None


class ModelCache:
    """
    Intelligent model caching with memory management.
    """
    
    def __init__(self, max_size: int = 100, max_memory_gb: float = 2.0):
        self.max_size = max_size
        self.max_memory_gb = max_memory_gb
        self.cache: Dict[str, Any] = {}
        self.metadata: Dict[str, ModelMetadata] = {}
        self.access_times: Dict[str, datetime] = {}
        self.lock = Lock()
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
    
    def get(self, key: str) -> Optional[Any]:
        """Get model from cache"""
        with self.lock:
            if key in self.cache:
                self.access_times[key] = datetime.now()
                self.logger.debug(f"Cache hit for model: {key}")
                return self.cache[key]
            
            self.logger.debug(f"Cache miss for model: {key}")
            return None
    
    def put(self, key: str, model: Any, metadata: ModelMetadata):
        """Put model in cache with intelligent eviction"""
        with self.lock:
            # Check memory usage before adding
            if self._should_evict_for_memory():
                self._evict_by_memory_pressure()
            
            # Check size limit
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            self.cache[key] = model
            self.metadata[key] = metadata
            self.access_times[key] = datetime.now()
            
            self.logger.info(f"Cached model: {key}")
            self.performance_logger.log_metric(
                "model_cache_size",
                len(self.cache),
                cache_memory_mb=self._get_memory_usage_mb()
            )
    
    def remove(self, key: str):
        """Remove model from cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                del self.metadata[key]
                del self.access_times[key]
                self.logger.info(f"Removed model from cache: {key}")
    
    def clear(self):
        """Clear all cached models"""
        with self.lock:
            self.cache.clear()
            self.metadata.clear()
            self.access_times.clear()
            gc.collect()  # Force garbage collection
            self.logger.info("Cleared model cache")
    
    def _should_evict_for_memory(self) -> bool:
        """Check if we should evict models due to memory pressure"""
        memory_usage_gb = self._get_memory_usage_gb()
        return memory_usage_gb > self.max_memory_gb
    
    def _get_memory_usage_gb(self) -> float:
        """Get current memory usage in GB"""
        process = psutil.Process()
        return process.memory_info().rss / (1024 ** 3)
    
    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB"""
        return self._get_memory_usage_gb() * 1024
    
    def _evict_lru(self):
        """Evict least recently used model"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self.remove(lru_key)
        self.logger.info(f"Evicted LRU model: {lru_key}")
    
    def _evict_by_memory_pressure(self):
        """Evict models to reduce memory pressure"""
        # Sort by access time and remove oldest
        sorted_keys = sorted(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # Remove up to 25% of cached models
        evict_count = max(1, len(sorted_keys) // 4)
        
        for key in sorted_keys[:evict_count]:
            self.remove(key)
        
        gc.collect()  # Force garbage collection
        self.logger.info(f"Evicted {evict_count} models due to memory pressure")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'memory_usage_mb': self._get_memory_usage_mb(),
                'max_memory_gb': self.max_memory_gb,
                'models': list(self.cache.keys())
            }


class ModelManager:
    """
    Enhanced model management with intelligent loading and caching.
    """
    
    def __init__(self):
        self.config = config_manager.get_section('models')
        self.models_dir = Path(self.config.models_directory)
        self.cache = ModelCache(
            max_size=self.config.cache_size,
            max_memory_gb=self.config.max_memory_usage
        )
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        self._model_registry: Dict[str, ModelMetadata] = {}
        self._load_model_registry()
    
    @handle_exception
    def _load_model_registry(self):
        """Load model registry from disk"""
        registry_file = self.models_dir / "model_registry.json"
        
        if registry_file.exists():
            try:
                import json
                with open(registry_file, 'r') as f:
                    registry_data = json.load(f)
                
                for model_id, data in registry_data.items():
                    self._model_registry[model_id] = ModelMetadata(
                        name=data['name'],
                        path=data['path'],
                        model_type=data['model_type'],
                        version=data['version'],
                        created_at=datetime.fromisoformat(data['created_at']),
                        last_used=datetime.fromisoformat(data['last_used']),
                        accuracy=data.get('accuracy'),
                        size_mb=data.get('size_mb'),
                        feature_count=data.get('feature_count'),
                        target_type=data.get('target_type'),
                        training_data_hash=data.get('training_data_hash')
                    )
                
                self.logger.info(f"Loaded {len(self._model_registry)} models from registry")
                
            except Exception as e:
                self.logger.warning(f"Could not load model registry: {e}")
    
    def _save_model_registry(self):
        """Save model registry to disk"""
        registry_file = self.models_dir / "model_registry.json"
        registry_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            import json
            registry_data = {}
            
            for model_id, metadata in self._model_registry.items():
                registry_data[model_id] = {
                    'name': metadata.name,
                    'path': metadata.path,
                    'model_type': metadata.model_type,
                    'version': metadata.version,
                    'created_at': metadata.created_at.isoformat(),
                    'last_used': metadata.last_used.isoformat(),
                    'accuracy': metadata.accuracy,
                    'size_mb': metadata.size_mb,
                    'feature_count': metadata.feature_count,
                    'target_type': metadata.target_type,
                    'training_data_hash': metadata.training_data_hash
                }
            
            with open(registry_file, 'w') as f:
                json.dump(registry_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Could not save model registry: {e}")
    
    @handle_exception
    def load_model(self, model_path: str, model_id: Optional[str] = None) -> Any:
        """
        Load a model with intelligent caching.
        
        Args:
            model_path: Path to the model file
            model_id: Optional model identifier for caching
            
        Returns:
            Loaded model object
        """
        if model_id is None:
            model_id = self._generate_model_id(model_path)
        
        # Check cache first
        if self.config.lazy_loading:
            cached_model = self.cache.get(model_id)
            if cached_model is not None:
                self._update_last_used(model_id)
                return cached_model
        
        # Load model from disk
        with self.performance_logger.timer("model_loading", model_path=model_path):
            try:
                model_file = Path(model_path)
                if not model_file.exists():
                    raise ModelError(
                        f"Model file not found: {model_path}",
                        context={'path': model_path},
                        recovery_suggestions=[
                            "Check if the model file exists",
                            "Verify the model path is correct",
                            "Train the model if it doesn't exist"
                        ]
                    )
                
                # Load the model
                model = joblib.load(model_file)
                
                # Create metadata
                metadata = self._create_model_metadata(model_path, model)
                
                # Cache the model if lazy loading is enabled
                if self.config.lazy_loading:
                    self.cache.put(model_id, model, metadata)
                
                # Update registry
                self._model_registry[model_id] = metadata
                self._save_model_registry()
                
                self.logger.info(f"Loaded model: {model_path}")
                return model
                
            except Exception as e:
                raise ModelError(
                    f"Failed to load model: {model_path}",
                    context={'path': model_path, 'error': str(e)},
                    original_exception=e,
                    recovery_suggestions=[
                        "Check if the model file is corrupted",
                        "Verify the model format is supported",
                        "Retrain the model if necessary"
                    ]
                )
    
    def _generate_model_id(self, model_path: str) -> str:
        """Generate a unique model ID from path"""
        return hashlib.md5(model_path.encode()).hexdigest()
    
    def _create_model_metadata(self, model_path: str, model: Any) -> ModelMetadata:
        """Create metadata for a model"""
        model_file = Path(model_path)
        
        # Get file size
        size_mb = model_file.stat().st_size / (1024 * 1024)
        
        # Try to extract model information
        model_type = type(model).__name__
        feature_count = None
        
        # Try to get feature count for sklearn models
        if hasattr(model, 'n_features_in_'):
            feature_count = model.n_features_in_
        elif hasattr(model, 'feature_importances_'):
            feature_count = len(model.feature_importances_)
        
        return ModelMetadata(
            name=model_file.stem,
            path=str(model_path),
            model_type=model_type,
            version="1.0",  # Default version
            created_at=datetime.fromtimestamp(model_file.stat().st_ctime),
            last_used=datetime.now(),
            size_mb=size_mb,
            feature_count=feature_count
        )
    
    def _update_last_used(self, model_id: str):
        """Update last used timestamp for a model"""
        if model_id in self._model_registry:
            self._model_registry[model_id].last_used = datetime.now()
    
    @handle_exception
    def predict(self, model_path: str, features: Union[np.ndarray, pd.DataFrame], 
                model_id: Optional[str] = None) -> np.ndarray:
        """
        Make predictions using a model.
        
        Args:
            model_path: Path to the model file
            features: Input features for prediction
            model_id: Optional model identifier
            
        Returns:
            Model predictions
        """
        model = self.load_model(model_path, model_id)
        
        # Validate features
        if isinstance(features, pd.DataFrame):
            features = features.values
        
        if not isinstance(features, np.ndarray):
            raise ValidationError(
                "Features must be numpy array or pandas DataFrame",
                field_name="features",
                expected_type="numpy.ndarray or pandas.DataFrame"
            )
        
        # Make prediction with timeout
        with self.performance_logger.timer("model_prediction", model_path=model_path):
            try:
                predictions = model.predict(features)
                
                self.performance_logger.log_counter(
                    "model_predictions",
                    increment=len(predictions) if hasattr(predictions, '__len__') else 1,
                    model_path=model_path
                )
                
                return predictions
                
            except Exception as e:
                raise ModelError(
                    f"Prediction failed for model: {model_path}",
                    context={
                        'model_path': model_path,
                        'features_shape': features.shape,
                        'error': str(e)
                    },
                    original_exception=e,
                    recovery_suggestions=[
                        "Check feature dimensions match model expectations",
                        "Verify feature preprocessing is correct",
                        "Check model compatibility"
                    ]
                )
    
    def get_model_info(self, model_id: str) -> Optional[ModelMetadata]:
        """Get metadata for a model"""
        return self._model_registry.get(model_id)
    
    def list_models(self) -> List[ModelMetadata]:
        """List all registered models"""
        return list(self._model_registry.values())
    
    def cleanup_cache(self):
        """Clean up model cache"""
        self.cache.clear()
        gc.collect()
        self.logger.info("Model cache cleaned up")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return self.cache.get_stats()
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on model manager"""
        try:
            stats = self.get_cache_stats()
            model_count = len(self._model_registry)
            
            return {
                'status': 'healthy',
                'model_count': model_count,
                'cache_stats': stats,
                'models_directory_exists': self.models_dir.exists()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }


# Global model manager instance
model_manager = ModelManager()
