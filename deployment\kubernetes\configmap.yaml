# ConfigMap for NoryonAI Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: noryonai-config
  namespace: noryonai
  labels:
    app: noryonai-backend
data:
  config.yaml: |
    environment: production
    debug: false
    version: "2.0.0"
    
    database:
      host: postgres-service
      port: 5432
      database: noryonai
      username: noryonai_user
      pool_size: 20
      max_overflow: 40
      pool_timeout: 30
    
    models:
      models_directory: /app/models
      cache_size: 200
      lazy_loading: true
      prediction_timeout: 30
      batch_size: 64
      max_memory_usage: 4.0
    
    trading:
      initial_capital: 100000.0
      max_position_size: 0.1
      risk_threshold: 0.03
      min_confidence: 0.6
      stop_loss: 0.02
      take_profit: 0.04
      max_daily_trades: 100
    
    data:
      data_directory: /app/data
      chunk_size_mb: 256
      max_memory_gb: 16.0
      max_workers: 16
      cache_enabled: true
      cache_expiry: 3600
    
    logging:
      level: INFO
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      log_to_file: true
      log_file: /app/logs/noryonai.log
      max_file_size_mb: 50
      backup_count: 10
      log_rotation: true
    
    security:
      jwt_expiry: 3600
      rate_limit_per_minute: 1000
      enable_cors: true
      allowed_origins:
        - "https://noryonai.com"
        - "https://api.noryonai.com"
      encryption_enabled: true
    
    enable_trading: true
    enable_monitoring: true
    enable_api: true
    enable_web_interface: true

---
# ConfigMap for Nginx Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: noryonai
  labels:
    app: noryonai-backend
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream noryonai_backend {
            server noryonai-service:8000;
        }
        
        server {
            listen 80;
            server_name _;
            
            # Redirect HTTP to HTTPS
            return 301 https://$server_name$request_uri;
        }
        
        server {
            listen 443 ssl http2;
            server_name _;
            
            ssl_certificate /etc/nginx/ssl/tls.crt;
            ssl_certificate_key /etc/nginx/ssl/tls.key;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers HIGH:!aNULL:!MD5;
            
            # Security headers
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
            
            # Rate limiting
            limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
            
            location / {
                limit_req zone=api burst=20 nodelay;
                
                proxy_pass http://noryonai_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Timeouts
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            location /health {
                proxy_pass http://noryonai_backend/health;
                access_log off;
            }
        }
    }
