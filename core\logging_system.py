"""
Enhanced Logging System
Provides structured, contextual, and performance-aware logging.
"""

import logging
import logging.handlers
import json
import sys
import traceback
from typing import Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path
from contextlib import contextmanager
from functools import wraps

from core.exceptions import SystemError, handle_exception
from core.config_manager import config_manager


class StructuredFormatter(logging.Formatter):
    """
    Structured JSON formatter for logs with enhanced context.
    """
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add custom fields from extra
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry, default=str)


class PerformanceLogger:
    """
    Performance monitoring and logging.
    """
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = logging.getLogger(logger_name)
    
    @contextmanager
    def timer(self, operation: str, **context):
        """Context manager for timing operations"""
        start_time = datetime.now()
        try:
            yield
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.info(
                f"Operation completed: {operation}",
                extra={
                    'operation': operation,
                    'duration_seconds': duration,
                    'status': 'success',
                    **context
                }
            )
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.error(
                f"Operation failed: {operation}",
                extra={
                    'operation': operation,
                    'duration_seconds': duration,
                    'status': 'error',
                    'error': str(e),
                    **context
                }
            )
            raise
    
    def log_metric(self, metric_name: str, value: Union[int, float], **context):
        """Log a performance metric"""
        self.logger.info(
            f"Metric: {metric_name}",
            extra={
                'metric_name': metric_name,
                'metric_value': value,
                'metric_type': 'gauge',
                **context
            }
        )
    
    def log_counter(self, counter_name: str, increment: int = 1, **context):
        """Log a counter increment"""
        self.logger.info(
            f"Counter: {counter_name}",
            extra={
                'counter_name': counter_name,
                'increment': increment,
                'metric_type': 'counter',
                **context
            }
        )


class AuditLogger:
    """
    Audit logging for security and compliance.
    """
    
    def __init__(self, logger_name: str = "audit"):
        self.logger = logging.getLogger(logger_name)
    
    def log_user_action(self, user_id: str, action: str, resource: str = None, **context):
        """Log user actions for audit trail"""
        self.logger.info(
            f"User action: {action}",
            extra={
                'audit_type': 'user_action',
                'user_id': user_id,
                'action': action,
                'resource': resource,
                'timestamp': datetime.now().isoformat(),
                **context
            }
        )
    
    def log_system_event(self, event_type: str, description: str, **context):
        """Log system events for audit trail"""
        self.logger.info(
            f"System event: {event_type}",
            extra={
                'audit_type': 'system_event',
                'event_type': event_type,
                'description': description,
                'timestamp': datetime.now().isoformat(),
                **context
            }
        )
    
    def log_security_event(self, event_type: str, severity: str, description: str, **context):
        """Log security events"""
        log_level = getattr(logging, severity.upper(), logging.WARNING)
        self.logger.log(
            log_level,
            f"Security event: {event_type}",
            extra={
                'audit_type': 'security_event',
                'event_type': event_type,
                'severity': severity,
                'description': description,
                'timestamp': datetime.now().isoformat(),
                **context
            }
        )


class LoggingManager:
    """
    Central logging management and configuration.
    """
    
    def __init__(self):
        self.configured = False
        self.performance_logger = None
        self.audit_logger = None
    
    @handle_exception
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        if self.configured:
            return
        
        config = config_manager.get_section('logging')
        
        # Create logs directory
        log_dir = Path(config.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with colored output
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, config.level.upper()))
        
        if config_manager.get_config().environment.value == 'development':
            # Use simple format for development
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        else:
            # Use structured format for production
            console_formatter = StructuredFormatter()
        
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler with rotation
        if config.log_to_file:
            if config.log_rotation:
                file_handler = logging.handlers.RotatingFileHandler(
                    config.log_file,
                    maxBytes=config.max_file_size_mb * 1024 * 1024,
                    backupCount=config.backup_count
                )
            else:
                file_handler = logging.FileHandler(config.log_file)
            
            file_handler.setLevel(logging.DEBUG)  # File gets all logs
            file_formatter = StructuredFormatter()
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
        
        # Setup specialized loggers
        self._setup_performance_logger()
        self._setup_audit_logger()
        
        # Setup error tracking
        self._setup_error_tracking()
        
        self.configured = True
        logging.info("Logging system initialized successfully")
    
    def _setup_performance_logger(self):
        """Setup performance logging"""
        perf_logger = logging.getLogger('performance')
        perf_logger.setLevel(logging.INFO)
        
        # Separate file for performance logs
        perf_file = Path(config_manager.get_section('logging').log_file).parent / 'performance.log'
        perf_handler = logging.handlers.RotatingFileHandler(
            perf_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=5
        )
        perf_handler.setFormatter(StructuredFormatter())
        perf_logger.addHandler(perf_handler)
        
        self.performance_logger = PerformanceLogger()
    
    def _setup_audit_logger(self):
        """Setup audit logging"""
        audit_logger = logging.getLogger('audit')
        audit_logger.setLevel(logging.INFO)
        
        # Separate file for audit logs
        audit_file = Path(config_manager.get_section('logging').log_file).parent / 'audit.log'
        audit_handler = logging.handlers.RotatingFileHandler(
            audit_file,
            maxBytes=100 * 1024 * 1024,  # 100MB
            backupCount=10
        )
        audit_handler.setFormatter(StructuredFormatter())
        audit_logger.addHandler(audit_handler)
        
        self.audit_logger = AuditLogger()
    
    def _setup_error_tracking(self):
        """Setup error tracking and alerting"""
        error_logger = logging.getLogger('errors')
        error_logger.setLevel(logging.ERROR)
        
        # Separate file for errors
        error_file = Path(config_manager.get_section('logging').log_file).parent / 'errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10
        )
        error_handler.setFormatter(StructuredFormatter())
        error_logger.addHandler(error_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger with the specified name"""
        if not self.configured:
            self.setup_logging()
        return logging.getLogger(name)
    
    def get_performance_logger(self) -> PerformanceLogger:
        """Get the performance logger"""
        if not self.configured:
            self.setup_logging()
        return self.performance_logger
    
    def get_audit_logger(self) -> AuditLogger:
        """Get the audit logger"""
        if not self.configured:
            self.setup_logging()
        return self.audit_logger


def log_function_call(include_args: bool = False, include_result: bool = False):
    """
    Decorator to log function calls with optional arguments and results.
    
    Args:
        include_args: Whether to log function arguments
        include_result: Whether to log function result
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(func.__module__)
            
            log_data = {
                'function': func.__name__,
                'module': func.__module__
            }
            
            if include_args:
                log_data['args'] = str(args)[:200]  # Limit length
                log_data['kwargs'] = {k: str(v)[:100] for k, v in kwargs.items()}
            
            logger.debug(f"Calling function: {func.__name__}", extra=log_data)
            
            try:
                result = func(*args, **kwargs)
                
                if include_result:
                    log_data['result'] = str(result)[:200]  # Limit length
                
                logger.debug(f"Function completed: {func.__name__}", extra=log_data)
                return result
                
            except Exception as e:
                log_data['error'] = str(e)
                logger.error(f"Function failed: {func.__name__}", extra=log_data)
                raise
        
        return wrapper
    return decorator


# Global logging manager instance
logging_manager = LoggingManager()

# Convenience functions
def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    return logging_manager.get_logger(name)

def get_performance_logger() -> PerformanceLogger:
    """Get the performance logger"""
    return logging_manager.get_performance_logger()

def get_audit_logger() -> AuditLogger:
    """Get the audit logger"""
    return logging_manager.get_audit_logger()

def setup_logging():
    """Setup the logging system"""
    logging_manager.setup_logging()
