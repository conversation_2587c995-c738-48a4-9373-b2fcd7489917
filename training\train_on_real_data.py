import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from training.real_data_loader import RealTradingDataLoader
from training.safe_training_system import SafeTrainingSystem
import numpy as np
import pandas as pd
from datetime import datetime
import json
import pickle
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

def train_models_on_real_data():
    """Train all models on real trading data"""
    
    print("=" * 80)
    print("TRAINING MODELS ON REAL TRADING DATA")
    print("=" * 80)
    
    # Initialize data loader
    print("\n1. Loading real trading data...")
    loader = RealTradingDataLoader("data/paperswithbacktestStocks-1Min-Price")
    
    # Load 500k samples for training (manageable size)
    X, y = loader.load_and_prepare(sample_size=500000)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"\nTraining set size: {len(X_train)}")
    print(f"Test set size: {len(X_test)}")
    print(f"Features: {X_train.shape[1]}")
    
    # Initialize training system
    print("\n2. Initializing training system...")
    trainer = SafeTrainingSystem()
    
    # Train models
    print("\n3. Training models...")
    models = trainer.train_ensemble(X_train, y_train)
    
    # Evaluate models
    print("\n4. Evaluating models on test data...")
    results = {}
    
    for name, model in models.items():
        print(f"\n{name} Performance:")
        y_pred = model.predict(X_test)
        
        # Calculate metrics
        accuracy = (y_pred == y_test).mean()
        report = classification_report(y_test, y_pred, output_dict=True)
        
        print(f"Accuracy: {accuracy:.4f}")
        print(f"Precision (Buy): {report['1']['precision']:.4f}")
        print(f"Recall (Buy): {report['1']['recall']:.4f}")
        print(f"F1-Score (Buy): {report['1']['f1-score']:.4f}")
        
        results[name] = {
            'accuracy': accuracy,
            'precision_buy': report['1']['precision'],
            'recall_buy': report['1']['recall'],
            'f1_buy': report['1']['f1-score'],
            'precision_sell': report['0']['precision'],
            'recall_sell': report['0']['recall'],
            'f1_sell': report['0']['f1-score']
        }
    
    # Make ensemble predictions
    print("\n5. Testing ensemble predictions...")
    predictions = trainer.predict_ensemble(X_test[:100])  # Test on 100 samples
    
    # Analyze predictions
    buy_signals = sum(1 for p in predictions if p['signal'] == 'BUY')
    sell_signals = sum(1 for p in predictions if p['signal'] == 'SELL')
    hold_signals = sum(1 for p in predictions if p['signal'] == 'HOLD')
    
    print(f"\nEnsemble predictions on 100 samples:")
    print(f"BUY signals: {buy_signals}")
    print(f"SELL signals: {sell_signals}")
    print(f"HOLD signals: {hold_signals}")
    
    # Save models and results
    print("\n6. Saving trained models...")
    
    # Create models directory
    os.makedirs("models/real_data", exist_ok=True)
    
    # Save models
    for name, model in models.items():
        model_path = f"models/real_data/{name}_real_data.pkl"
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        print(f"Saved {name} to {model_path}")
    
    # Save scaler
    scaler_path = "models/real_data/scaler.pkl"
    with open(scaler_path, 'wb') as f:
        pickle.dump(loader.scaler, f)
    print(f"Saved scaler to {scaler_path}")
    
    # Save symbol encodings
    encoding_path = "models/real_data/symbol_encodings.json"
    with open(encoding_path, 'w') as f:
        json.dump(loader.symbol_encodings, f)
    print(f"Saved symbol encodings to {encoding_path}")
    
    # Save results
    results_path = "models/real_data/training_results.json"
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Saved results to {results_path}")
    
    # Generate trading signals for recent data
    print("\n7. Generating trading signals...")
    
    # Get the last 1000 samples
    recent_data = X_test[-1000:]
    recent_predictions = trainer.predict_ensemble(recent_data)
    
    # Analyze signal distribution
    signal_counts = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
    confidence_scores = []
    
    for pred in recent_predictions:
        signal_counts[pred['signal']] += 1
        confidence_scores.append(pred['confidence'])
    
    print("\nSignal distribution for recent 1000 samples:")
    for signal, count in signal_counts.items():
        percentage = (count / len(recent_predictions)) * 100
        print(f"{signal}: {count} ({percentage:.1f}%)")
    
    avg_confidence = np.mean(confidence_scores)
    print(f"\nAverage confidence: {avg_confidence:.3f}")
    
    # Find high-confidence trades
    high_confidence_trades = [
        pred for pred in recent_predictions 
        if pred['confidence'] > 0.7 and pred['signal'] != 'HOLD'
    ]
    
    print(f"\nHigh-confidence trades (>70%): {len(high_confidence_trades)}")
    
    # Sample high-confidence predictions
    if high_confidence_trades:
        print("\nSample high-confidence predictions:")
        for i, pred in enumerate(high_confidence_trades[:5]):
            print(f"  {i+1}. {pred['signal']} - Confidence: {pred['confidence']:.3f}")
            print(f"     Model votes: {pred['model_predictions']}")
    
    print("\n" + "=" * 80)
    print("TRAINING COMPLETE!")
    print("=" * 80)
    
    return models, results

def test_live_prediction(models, scaler, symbol_encodings):
    """Test prediction on a single sample"""
    print("\n8. Testing live prediction capability...")
    
    # Create a sample data point (you would get this from real-time feed)
    sample_features = {
        'returns': 0.002,
        'log_returns': 0.0019,
        'high_low_ratio': 1.015,
        'close_open_ratio': 1.003,
        'volume_ratio': 1.2,
        'price_to_sma_5': 1.01,
        'price_to_sma_10': 1.008,
        'price_to_sma_20': 1.005,
        'price_to_sma_50': 0.998,
        'macd': 0.15,
        'macd_signal': 0.12,
        'macd_histogram': 0.03,
        'rsi': 55,
        'bb_position': 0.6,
        'atr': 2.5,
        'price_position': 0.7,
        'hour': 11,
        'day_of_week': 2,
        'is_market_open': 1,
        'symbol_encoded': 0  # AAPL = 0
    }
    
    # Convert to array
    feature_array = np.array([list(sample_features.values())])
    
    # Scale features
    feature_scaled = scaler.transform(feature_array)
    
    # Make predictions with each model
    predictions = {}
    for name, model in models.items():
        pred = model.predict(feature_scaled)[0]
        prob = model.predict_proba(feature_scaled)[0]
        predictions[name] = {
            'prediction': 'BUY' if pred == 1 else 'SELL',
            'confidence': max(prob)
        }
    
    print("\nLive prediction test:")
    for name, pred in predictions.items():
        print(f"{name}: {pred['prediction']} (confidence: {pred['confidence']:.3f})")
    
    # Ensemble decision
    buy_votes = sum(1 for p in predictions.values() if p['prediction'] == 'BUY')
    sell_votes = len(predictions) - buy_votes
    
    if buy_votes > sell_votes:
        final_signal = 'BUY'
    elif sell_votes > buy_votes:
        final_signal = 'SELL'
    else:
        final_signal = 'HOLD'
    
    print(f"\nFinal ensemble signal: {final_signal}")
    print(f"Votes - BUY: {buy_votes}, SELL: {sell_votes}")

if __name__ == "__main__":
    # Train models
    models, results = train_models_on_real_data()
    
    # Load saved components for live prediction test
    import pickle
    
    with open("models/real_data/scaler.pkl", 'rb') as f:
        scaler = pickle.load(f)
    
    with open("models/real_data/symbol_encodings.json", 'r') as f:
        symbol_encodings = json.load(f)
    
    # Test live prediction
    test_live_prediction(models, scaler, symbol_encodings) 