#!/usr/bin/env python
"""
NORYONAI Data Quality Validator

Comprehensive data validation and quality assurance for financial datasets.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import os
import json
from datetime import datetime, timedelta
import warnings
import pyarrow as pa
import pyarrow.parquet as pq

class DataQualityValidator:
    """Comprehensive data quality validation for financial datasets"""
    
    def __init__(self):
        self.validation_results = {}
        self.quality_metrics = {}
        
    def validate_dataset(self, data_path: str, dataset_name: str = None) -> Dict[str, Any]:
        """Comprehensive validation of a financial dataset"""
        
        if dataset_name is None:
            dataset_name = os.path.basename(data_path)
            
        print(f"🔍 Validating dataset: {dataset_name}")
        print(f"📁 Path: {data_path}")
        
        validation_report = {
            'dataset_name': dataset_name,
            'data_path': data_path,
            'timestamp': datetime.now().isoformat(),
            'file_validation': {},
            'data_validation': {},
            'quality_metrics': {},
            'recommendations': [],
            'overall_score': 0.0
        }
        
        try:
            # 1. File-level validation
            validation_report['file_validation'] = self._validate_files(data_path)
            
            # 2. Load and validate data content
            if validation_report['file_validation']['files_accessible']:
                df = self._load_dataset(data_path)
                if df is not None:
                    validation_report['data_validation'] = self._validate_data_content(df)
                    validation_report['quality_metrics'] = self._calculate_quality_metrics(df)
                    validation_report['recommendations'] = self._generate_recommendations(df, validation_report)
                    
            # 3. Calculate overall quality score
            validation_report['overall_score'] = self._calculate_overall_score(validation_report)
            
        except Exception as e:
            validation_report['error'] = str(e)
            validation_report['overall_score'] = 0.0
            
        self.validation_results[dataset_name] = validation_report
        return validation_report
        
    def _validate_files(self, data_path: str) -> Dict[str, Any]:
        """Validate file structure and accessibility"""
        
        file_validation = {
            'path_exists': False,
            'files_found': 0,
            'total_size_mb': 0,
            'file_types': {},
            'files_accessible': False,
            'structure_valid': False
        }
        
        if not os.path.exists(data_path):
            return file_validation
            
        file_validation['path_exists'] = True
        
        # Count files and calculate sizes
        total_size = 0
        file_types = {}
        accessible_files = 0
        
        if os.path.isfile(data_path):
            # Single file
            try:
                size = os.path.getsize(data_path)
                total_size += size
                ext = os.path.splitext(data_path)[1].lower()
                file_types[ext] = file_types.get(ext, 0) + 1
                accessible_files = 1
            except:
                pass
        else:
            # Directory
            for root, dirs, files in os.walk(data_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        size = os.path.getsize(file_path)
                        total_size += size
                        ext = os.path.splitext(file)[1].lower()
                        file_types[ext] = file_types.get(ext, 0) + 1
                        accessible_files += 1
                    except:
                        continue
                        
        file_validation['files_found'] = accessible_files
        file_validation['total_size_mb'] = round(total_size / (1024 * 1024), 2)
        file_validation['file_types'] = file_types
        file_validation['files_accessible'] = accessible_files > 0
        file_validation['structure_valid'] = self._validate_structure(data_path, file_types)
        
        return file_validation
        
    def _validate_structure(self, data_path: str, file_types: Dict[str, int]) -> bool:
        """Validate expected file structure"""
        
        # Check for expected financial data file types
        expected_types = ['.arrow', '.parquet', '.csv', '.json']
        has_expected_type = any(ext in file_types for ext in expected_types)
        
        # Check for train subdirectory (common pattern)
        if os.path.isdir(data_path):
            train_path = os.path.join(data_path, 'train')
            if os.path.exists(train_path):
                return True
                
        return has_expected_type
        
    def _load_dataset(self, data_path: str) -> Optional[pd.DataFrame]:
        """Load dataset for content validation"""
        
        try:
            # Try different loading methods based on structure
            if os.path.isfile(data_path):
                # Single file
                if data_path.endswith('.csv'):
                    return pd.read_csv(data_path, nrows=10000)  # Sample for validation
                elif data_path.endswith('.parquet'):
                    return pd.read_parquet(data_path)
                elif data_path.endswith('.arrow'):
                    try:
                        # Try different Arrow loading methods
                        table = pa.ipc.open_file(data_path).read_all()
                        return table.to_pandas()
                    except:
                        # Try as feather format
                        return pd.read_feather(data_path)
                    
            else:
                # Directory - try to load from train subdirectory
                train_path = os.path.join(data_path, 'train')
                if os.path.exists(train_path):
                    # Load arrow files
                    arrow_files = [f for f in os.listdir(train_path) if f.endswith('.arrow')]
                    if arrow_files:
                        # Try loading first arrow file for validation
                        first_file = os.path.join(train_path, arrow_files[0])
                        try:
                            # Try IPC format first
                            table = pa.ipc.open_file(first_file).read_all()
                            return table.to_pandas()
                        except:
                            try:
                                # Try feather format
                                return pd.read_feather(first_file)
                            except:
                                try:
                                    # Try parquet format
                                    return pd.read_parquet(first_file)
                                except:
                                    print(f"⚠️ Could not read Arrow file: {first_file}")
                                    return None
                        
                # Try loading parquet files
                parquet_files = []
                for root, dirs, files in os.walk(data_path):
                    parquet_files.extend([os.path.join(root, f) for f in files if f.endswith('.parquet')])
                    
                if parquet_files:
                    return pd.read_parquet(parquet_files[0])
                    
                # Try loading CSV files
                csv_files = []
                for root, dirs, files in os.walk(data_path):
                    csv_files.extend([os.path.join(root, f) for f in files if f.endswith('.csv')])
                    
                if csv_files:
                    return pd.read_csv(csv_files[0], nrows=10000)
                    
        except Exception as e:
            print(f"⚠️ Error loading dataset: {e}")
            return None
            
        return None
        
    def _validate_data_content(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate data content and structure"""
        
        validation = {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'missing_values': df.isnull().sum().to_dict(),
            'duplicate_rows': df.duplicated().sum(),
            'date_columns': [],
            'price_columns': [],
            'volume_columns': [],
            'required_columns_present': False,
            'data_consistency': {},
            'outliers': {}
        }
        
        # Identify column types
        for col in df.columns:
            col_lower = col.lower()
            
            # Date columns
            if any(date_word in col_lower for date_word in ['date', 'time', 'timestamp']):
                validation['date_columns'].append(col)
                
            # Price columns
            if any(price_word in col_lower for price_word in ['price', 'open', 'high', 'low', 'close', 'adj']):
                validation['price_columns'].append(col)
                
            # Volume columns
            if 'volume' in col_lower:
                validation['volume_columns'].append(col)
                
        # Check for required financial data columns
        required_patterns = ['close', 'price']
        validation['required_columns_present'] = any(
            any(pattern in col.lower() for pattern in required_patterns)
            for col in df.columns
        )
        
        # Data consistency checks
        validation['data_consistency'] = self._check_data_consistency(df)
        
        # Outlier detection
        validation['outliers'] = self._detect_outliers(df)
        
        return validation
        
    def _check_data_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check data consistency and logical constraints"""
        
        consistency = {
            'negative_prices': 0,
            'negative_volumes': 0,
            'high_low_consistency': True,
            'open_close_consistency': True,
            'extreme_returns': 0,
            'data_gaps': 0
        }
        
        # Check for negative prices
        price_cols = [col for col in df.columns if any(word in col.lower() for word in ['price', 'open', 'high', 'low', 'close'])]
        for col in price_cols:
            if df[col].dtype in ['float64', 'int64']:
                consistency['negative_prices'] += (df[col] < 0).sum()
                
        # Check for negative volumes
        volume_cols = [col for col in df.columns if 'volume' in col.lower()]
        for col in volume_cols:
            if df[col].dtype in ['float64', 'int64']:
                consistency['negative_volumes'] += (df[col] < 0).sum()
                
        # Check high >= low consistency
        if 'high' in df.columns and 'low' in df.columns:
            consistency['high_low_consistency'] = (df['high'] >= df['low']).all()
            
        # Check for extreme returns (>50% daily change)
        if 'close' in df.columns:
            returns = df['close'].pct_change().abs()
            consistency['extreme_returns'] = (returns > 0.5).sum()
            
        return consistency
        
    def _detect_outliers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Detect outliers in numerical columns"""
        
        outliers = {}
        
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        
        for col in numerical_cols:
            if df[col].notna().sum() > 0:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outlier_count = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                outlier_percentage = (outlier_count / len(df)) * 100
                
                outliers[col] = {
                    'count': int(outlier_count),
                    'percentage': round(outlier_percentage, 2),
                    'lower_bound': float(lower_bound),
                    'upper_bound': float(upper_bound)
                }
                
        return outliers
        
    def _calculate_quality_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive quality metrics"""
        
        metrics = {
            'completeness': {},
            'consistency': {},
            'validity': {},
            'uniqueness': {},
            'timeliness': {}
        }
        
        # Completeness (missing data)
        total_cells = df.shape[0] * df.shape[1]
        missing_cells = df.isnull().sum().sum()
        metrics['completeness']['overall_score'] = round((1 - missing_cells / total_cells) * 100, 2)
        metrics['completeness']['missing_percentage'] = round((missing_cells / total_cells) * 100, 2)
        
        # Consistency (data format and logical consistency)
        consistency_score = 100
        if 'high' in df.columns and 'low' in df.columns:
            inconsistent_high_low = (df['high'] < df['low']).sum()
            if inconsistent_high_low > 0:
                consistency_score -= 20
                
        metrics['consistency']['score'] = consistency_score
        
        # Validity (data type and range validity)
        validity_score = 100
        price_cols = [col for col in df.columns if any(word in col.lower() for word in ['price', 'open', 'high', 'low', 'close'])]
        for col in price_cols:
            if df[col].dtype in ['float64', 'int64']:
                negative_count = (df[col] < 0).sum()
                if negative_count > 0:
                    validity_score -= min(30, negative_count / len(df) * 100)
                    
        metrics['validity']['score'] = max(0, round(validity_score, 2))
        
        # Uniqueness (duplicate detection)
        duplicate_percentage = (df.duplicated().sum() / len(df)) * 100
        uniqueness_score = max(0, 100 - duplicate_percentage)
        metrics['uniqueness']['score'] = round(uniqueness_score, 2)
        metrics['uniqueness']['duplicate_percentage'] = round(duplicate_percentage, 2)
        
        # Timeliness (data recency - simplified)
        metrics['timeliness']['score'] = 85  # Default score, would need actual timestamp analysis
        
        return metrics
        
    def _generate_recommendations(self, df: pd.DataFrame, validation_report: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on validation results"""
        
        recommendations = []
        
        # Missing data recommendations
        missing_pct = validation_report['quality_metrics']['completeness']['missing_percentage']
        if missing_pct > 5:
            recommendations.append(f"High missing data ({missing_pct:.1f}%). Consider data imputation or filtering.")
            
        # Duplicate data recommendations
        duplicate_pct = validation_report['quality_metrics']['uniqueness']['duplicate_percentage']
        if duplicate_pct > 1:
            recommendations.append(f"Duplicate rows detected ({duplicate_pct:.1f}%). Remove duplicates before training.")
            
        # Outlier recommendations
        outliers = validation_report['data_validation']['outliers']
        high_outlier_cols = [col for col, info in outliers.items() if info['percentage'] > 5]
        if high_outlier_cols:
            recommendations.append(f"High outlier percentage in columns: {', '.join(high_outlier_cols)}. Consider outlier treatment.")
            
        # Data consistency recommendations
        consistency = validation_report['data_validation']['data_consistency']
        if consistency['negative_prices'] > 0:
            recommendations.append(f"Negative prices detected ({consistency['negative_prices']} instances). Investigate data source.")
            
        if not consistency['high_low_consistency']:
            recommendations.append("High < Low inconsistency detected. Verify data integrity.")
            
        # Column recommendations
        if not validation_report['data_validation']['required_columns_present']:
            recommendations.append("Missing required price columns. Ensure 'close' or 'price' column exists.")
            
        # Size recommendations
        if df.shape[0] < 1000:
            recommendations.append("Small dataset size. Consider collecting more data for robust training.")
            
        return recommendations
        
    def _calculate_overall_score(self, validation_report: Dict[str, Any]) -> float:
        """Calculate overall data quality score (0-100)"""
        
        if 'quality_metrics' not in validation_report:
            return 0.0
            
        metrics = validation_report['quality_metrics']
        
        # Weighted average of quality dimensions
        weights = {
            'completeness': 0.25,
            'consistency': 0.25,
            'validity': 0.25,
            'uniqueness': 0.15,
            'timeliness': 0.10
        }
        
        total_score = 0
        total_weight = 0
        
        for dimension, weight in weights.items():
            if dimension in metrics and 'score' in metrics[dimension]:
                total_score += metrics[dimension]['score'] * weight
                total_weight += weight
                
        return round(total_score / total_weight if total_weight > 0 else 0, 2)
        
    def validate_all_datasets(self, data_manifest_path: str = "data/DATA_MANIFEST.md") -> Dict[str, Any]:
        """Validate all datasets listed in the data manifest"""
        
        print("🔍 COMPREHENSIVE DATA QUALITY VALIDATION")
        print("=" * 60)
        
        # Priority datasets for Phase 1
        priority_datasets = [
            "data/paperswithbacktestStocks-1Min-Price",
            "data/sp500_news_290k_articles.csv",
            "data/pmoe7SP_500_Stocks_Data-ratios_news_price_10_yrs",
            "data/paperswithbacktestForex-Daily-Price",
            "data/paperswithbacktestETFs-Daily-Price"
        ]
        
        validation_summary = {
            'total_datasets': 0,
            'validated_datasets': 0,
            'high_quality_datasets': 0,
            'datasets': {},
            'recommendations': [],
            'timestamp': datetime.now().isoformat()
        }
        
        for dataset_path in priority_datasets:
            if os.path.exists(dataset_path):
                print(f"\n📊 Validating: {os.path.basename(dataset_path)}")
                
                validation_result = self.validate_dataset(dataset_path)
                dataset_name = os.path.basename(dataset_path)
                validation_summary['datasets'][dataset_name] = validation_result
                validation_summary['total_datasets'] += 1
                
                if validation_result['overall_score'] > 0:
                    validation_summary['validated_datasets'] += 1
                    
                if validation_result['overall_score'] >= 80:
                    validation_summary['high_quality_datasets'] += 1
                    
                # Print summary
                score = validation_result['overall_score']
                status = "🟢 EXCELLENT" if score >= 90 else "🟡 GOOD" if score >= 70 else "🟠 FAIR" if score >= 50 else "🔴 POOR"
                print(f"   Quality Score: {score:.1f}/100 {status}")
                
                if validation_result.get('recommendations'):
                    print(f"   Recommendations: {len(validation_result['recommendations'])} items")
                    
        # Generate overall recommendations
        validation_summary['recommendations'] = self._generate_overall_recommendations(validation_summary)
        
        # Save validation report
        report_path = f"data_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w') as f:
            json.dump(validation_summary, f, indent=2, default=str)
            
        print(f"\n📋 VALIDATION SUMMARY:")
        print(f"   Total Datasets: {validation_summary['total_datasets']}")
        print(f"   Successfully Validated: {validation_summary['validated_datasets']}")
        print(f"   High Quality (≥80): {validation_summary['high_quality_datasets']}")
        print(f"   Report saved: {report_path}")
        
        return validation_summary
        
    def _generate_overall_recommendations(self, validation_summary: Dict[str, Any]) -> List[str]:
        """Generate overall recommendations across all datasets"""
        
        recommendations = []
        
        high_quality_ratio = validation_summary['high_quality_datasets'] / validation_summary['total_datasets']
        
        if high_quality_ratio < 0.5:
            recommendations.append("Less than 50% of datasets meet high quality standards. Prioritize data cleaning.")
            
        if validation_summary['validated_datasets'] < validation_summary['total_datasets']:
            recommendations.append("Some datasets failed validation. Check file accessibility and format.")
            
        # Analyze common issues across datasets
        common_issues = {}
        for dataset_name, result in validation_summary['datasets'].items():
            for rec in result.get('recommendations', []):
                issue_type = rec.split('.')[0]  # Get first sentence as issue type
                common_issues[issue_type] = common_issues.get(issue_type, 0) + 1
                
        if common_issues:
            most_common = max(common_issues.items(), key=lambda x: x[1])
            recommendations.append(f"Most common issue: {most_common[0]} (affects {most_common[1]} datasets)")
            
        return recommendations

def main():
    """Run data quality validation"""
    
    validator = DataQualityValidator()
    
    # Run comprehensive validation
    summary = validator.validate_all_datasets()
    
    print("\n🎯 NEXT STEPS:")
    for i, rec in enumerate(summary['recommendations'], 1):
        print(f"   {i}. {rec}")

if __name__ == "__main__":
    main() 