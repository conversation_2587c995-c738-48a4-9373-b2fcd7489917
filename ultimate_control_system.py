#!/usr/bin/env python
"""
ULTIMATE NORYONAI CONTROL SYSTEM
Full control over every component with clear explanations
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import asyncio
import json
import os
from typing import Dict, List, Optional
import time

class NoryonaiControlCenter:
    """
    YOUR COMPLETE TRADING SYSTEM CONTROL
    Turn on/off any component you want!
    """
    
    def __init__(self):
        # CONTROL SWITCHES - Turn on/off anything!
        self.config = {
            'use_74_models': True,      # Your existing 74 AI models
            'use_qwen_local': True,     # Free local Qwen 14B
            'use_api_llms': False,      # Expensive API models
            'use_memory_protection': True,  # Prevent crashes
            'max_daily_api_cost': 5.0,  # Maximum $ to spend on APIs
            'min_confidence_threshold': 0.85,  # When to trust 74 models alone
        }
        
        # Component status
        self.components = {
            '74_models': {'loaded': False, 'status': 'Ready to load'},
            'qwen': {'loaded': False, 'status': 'Not loaded', 'trained': False},
            'api': {'available': False, 'keys_found': False}
        }
        
        # Qwen model (only loaded if enabled)
        self.qwen_model = None
        self.qwen_tokenizer = None
        
        # Stats
        self.decision_count = 0
        self.total_cost = 0.0
        
    def explain_system(self):
        """EXPLAINS EVERYTHING ABOUT YOUR SYSTEM"""
        
        print("=" * 70)
        print("🧠 NORYONAI COMPLETE SYSTEM EXPLANATION")
        print("=" * 70)
        
        print("\n📊 YOUR 74 AI MODELS:")
        print("- What: RandomForest, XGBoost, LSTM, Neural Networks")
        print("- Purpose: Fast pattern recognition, price prediction")
        print("- Speed: 100-1000 decisions/second")
        print("- Cost: $0 (already running)")
        print("- Training: ALREADY TRAINED on your 1.2TB data!")
        
        print("\n🤖 QWEN 14B LOCAL:")
        print("- What: Large Language Model (28GB)")
        print("- Purpose: Reasoning, validation, complex analysis")
        print("- Speed: 2-5 seconds/decision")
        print("- Cost: $0 (runs on your computer)")
        print("- Training: PRE-TRAINED by Alibaba on finance data")
        print("- Can fine-tune: YES (on your 1.2TB data later)")
        
        print("\n🌐 API LLMs (GPT-4, Claude):")
        print("- What: Cloud-based AI models")
        print("- Purpose: Handle extremely complex scenarios")
        print("- Speed: 1-3 seconds/decision")
        print("- Cost: $0.10-0.50 per call")
        print("- Training: Cannot train (closed models)")
        
        print("\n🔄 HOW IT ALL WORKS TOGETHER:")
        print("1. Market data comes in →")
        print("2. Your 74 models analyze (always) →")
        print("3. System decides:")
        print("   - High confidence? → Use 74 models result")
        print("   - Need reasoning? → Add Qwen (if enabled)")
        print("   - Super complex? → Use API (if enabled)")
        print("4. Execute trade with best decision")
        
        print("\n⚙️ CURRENT SETTINGS:")
        for key, value in self.config.items():
            print(f"- {key}: {value}")
            
    def change_settings(self, **kwargs):
        """Change any setting easily"""
        
        print("\n🔧 UPDATING SETTINGS:")
        for key, value in kwargs.items():
            if key in self.config:
                old_value = self.config[key]
                self.config[key] = value
                print(f"✅ {key}: {old_value} → {value}")
                
                # Handle special cases
                if key == 'use_qwen_local' and not value:
                    print("   → Qwen disabled, will use 74 models + API only")
                    self.unload_qwen()
                elif key == 'use_api_llms' and not value:
                    print("   → API disabled, will use local models only")
            else:
                print(f"❌ Unknown setting: {key}")
                
    def unload_qwen(self):
        """Free up memory by unloading Qwen"""
        if self.qwen_model is not None:
            del self.qwen_model
            del self.qwen_tokenizer
            self.qwen_model = None
            self.qwen_tokenizer = None
            
            # Force garbage collection
            import gc
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
            print("🗑️ Qwen unloaded, memory freed!")
            self.components['qwen']['loaded'] = False
            self.components['qwen']['status'] = 'Unloaded'
            
    async def make_decision(self, market_data: Dict) -> Dict:
        """
        MAIN DECISION FUNCTION
        Uses enabled components to make trading decisions
        """
        
        self.decision_count += 1
        print(f"\n{'='*50}")
        print(f"📈 DECISION #{self.decision_count}")
        print(f"{'='*50}")
        
        # Step 1: Always use 74 models (if enabled)
        if self.config['use_74_models']:
            ai_result = self.run_74_models(market_data)
            print(f"\n74 MODELS: {ai_result['decision']} "
                  f"(confidence: {ai_result['confidence']:.1%})")
            
            # Check if confidence is high enough
            if ai_result['confidence'] >= self.config['min_confidence_threshold']:
                print("✅ High confidence - using 74 models decision!")
                return self.format_decision(ai_result, source='74-Models')
        else:
            ai_result = {'decision': 'hold', 'confidence': 0.5}
            print("⚠️ 74 models disabled - using fallbacks")
            
        # Step 2: Try Qwen if enabled and confidence not high enough
        if self.config['use_qwen_local'] and ai_result['confidence'] < self.config['min_confidence_threshold']:
            try:
                qwen_result = await self.run_qwen_analysis(market_data)
                print(f"\nQWEN 14B: {qwen_result['decision']} "
                      f"(confidence: {qwen_result['confidence']:.1%})")
                
                # Combine results
                final_decision = self.combine_decisions(ai_result, qwen_result)
                return self.format_decision(final_decision, source='74-Models+Qwen')
                
            except Exception as e:
                print(f"❌ Qwen failed: {e}")
                
        # Step 3: Try API if enabled and still need help
        if self.config['use_api_llms'] and self.total_cost < self.config['max_daily_api_cost']:
            try:
                api_result = await self.run_api_analysis(market_data)
                print(f"\nAPI LLM: {api_result['decision']} "
                      f"(cost: ${api_result['cost']:.2f})")
                
                self.total_cost += api_result['cost']
                
                # Combine all available results
                final_decision = self.combine_all_decisions(ai_result, None, api_result)
                return self.format_decision(final_decision, source='74-Models+API')
                
            except Exception as e:
                print(f"❌ API failed: {e}")
                
        # Fallback: Use whatever we have
        print("\n⚠️ Using fallback decision")
        return self.format_decision(ai_result, source='Fallback')
        
    def run_74_models(self, market_data: Dict) -> Dict:
        """Simulate your 74 AI models"""
        
        # In reality, this calls your actual models
        # For demo, simulating the result
        
        import random
        
        # Simulate model voting
        buy_votes = random.randint(20, 50)
        sell_votes = random.randint(10, 30)
        hold_votes = 74 - buy_votes - sell_votes
        
        if buy_votes > sell_votes and buy_votes > hold_votes:
            decision = 'buy'
            confidence = buy_votes / 74
        elif sell_votes > buy_votes and sell_votes > hold_votes:
            decision = 'sell'
            confidence = sell_votes / 74
        else:
            decision = 'hold'
            confidence = hold_votes / 74
            
        return {
            'decision': decision,
            'confidence': confidence,
            'votes': f"{buy_votes}B/{sell_votes}S/{hold_votes}H"
        }
        
    async def run_qwen_analysis(self, market_data: Dict) -> Dict:
        """Run Qwen 14B analysis"""
        
        # Load Qwen if not loaded
        if self.qwen_model is None:
            print("Loading Qwen 14B...")
            
            try:
                self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                    "qwen ai",
                    trust_remote_code=True,
                    use_fast=False
                )
                
                self.qwen_model = AutoModelForCausalLM.from_pretrained(
                    "qwen ai",
                    torch_dtype=torch.float32,
                    device_map="cpu",
                    trust_remote_code=True,
                    low_cpu_mem_usage=True
                )
                
                self.components['qwen']['loaded'] = True
                self.components['qwen']['status'] = 'Loaded'
                print("✅ Qwen loaded!")
                
            except Exception as e:
                print(f"❌ Failed to load Qwen: {e}")
                raise
                
        # Generate analysis
        prompt = f"""Analyze: {market_data.get('pair')} at {market_data.get('price')}
RSI: {market_data.get('rsi')}, Trend: {market_data.get('trend')}
Decision (BUY/SELL/HOLD):"""

        inputs = self.qwen_tokenizer(prompt, return_tensors="pt", max_length=256, truncation=True)
        
        with torch.no_grad():
            outputs = self.qwen_model.generate(
                **inputs,
                max_new_tokens=50,
                temperature=0.7,
                pad_token_id=self.qwen_tokenizer.eos_token_id
            )
            
        response = self.qwen_tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Parse response
        decision = 'hold'
        if 'BUY' in response.upper():
            decision = 'buy'
        elif 'SELL' in response.upper():
            decision = 'sell'
            
        return {
            'decision': decision,
            'confidence': 0.75,  # Qwen confidence
            'reasoning': response[:100]
        }
        
    async def run_api_analysis(self, market_data: Dict) -> Dict:
        """Run API analysis (simulated)"""
        
        # In reality, this would call GPT-4 or Claude
        # Simulating for demo
        
        await asyncio.sleep(0.5)  # Simulate API delay
        
        return {
            'decision': 'hold',
            'confidence': 0.85,
            'cost': 0.10,
            'reasoning': 'Complex market conditions detected'
        }
        
    def combine_decisions(self, ai_result: Dict, llm_result: Dict) -> Dict:
        """Combine 74 models + LLM decisions"""
        
        # If they agree, high confidence
        if ai_result['decision'] == llm_result['decision']:
            return {
                'decision': ai_result['decision'],
                'confidence': (ai_result['confidence'] + llm_result['confidence']) / 2
            }
        else:
            # Disagreement - be cautious
            return {
                'decision': 'hold',
                'confidence': 0.6
            }
            
    def combine_all_decisions(self, ai_result: Dict, qwen_result: Optional[Dict], api_result: Optional[Dict]) -> Dict:
        """Combine all available decisions"""
        
        decisions = [ai_result]
        if qwen_result:
            decisions.append(qwen_result)
        if api_result:
            decisions.append(api_result)
            
        # Count votes
        vote_counts = {'buy': 0, 'sell': 0, 'hold': 0}
        total_confidence = 0
        
        for d in decisions:
            vote_counts[d['decision']] += 1
            total_confidence += d['confidence']
            
        # Get majority decision
        final_decision = max(vote_counts, key=vote_counts.get)
        avg_confidence = total_confidence / len(decisions)
        
        return {
            'decision': final_decision,
            'confidence': avg_confidence
        }
        
    def format_decision(self, decision: Dict, source: str) -> Dict:
        """Format final decision"""
        
        return {
            'decision': decision['decision'].upper(),
            'confidence': decision['confidence'],
            'source': source,
            'cost': self.total_cost,
            'components_used': {
                '74_models': self.config['use_74_models'],
                'qwen': self.config['use_qwen_local'] and self.components['qwen']['loaded'],
                'api': self.config['use_api_llms']
            }
        }
        
    def show_status(self):
        """Show current system status"""
        
        print("\n" + "="*70)
        print("📊 SYSTEM STATUS")
        print("="*70)
        
        print("\n🔧 ENABLED COMPONENTS:")
        print(f"- 74 AI Models: {'✅' if self.config['use_74_models'] else '❌'}")
        print(f"- Qwen Local: {'✅' if self.config['use_qwen_local'] else '❌'} "
              f"({self.components['qwen']['status']})")
        print(f"- API LLMs: {'✅' if self.config['use_api_llms'] else '❌'}")
        
        print(f"\n📈 STATISTICS:")
        print(f"- Decisions made: {self.decision_count}")
        print(f"- Total API cost: ${self.total_cost:.2f}")
        print(f"- Daily budget remaining: ${self.config['max_daily_api_cost'] - self.total_cost:.2f}")

# DEMO FUNCTION
async def demo_control_system():
    """Interactive demo of the control system"""
    
    print("🚀 NORYONAI ULTIMATE CONTROL SYSTEM")
    print("="*70)
    
    # Create control center
    control = NoryonaiControlCenter()
    
    # Explain everything
    control.explain_system()
    
    # Show how to change settings
    print("\n\n🎮 CONTROL EXAMPLES:")
    print("-"*70)
    
    # Example 1: Disable Qwen
    print("\n1️⃣ DISABLING QWEN (save memory):")
    control.change_settings(use_qwen_local=False)
    
    # Example 2: Enable API
    print("\n2️⃣ ENABLING API LLMS:")
    control.change_settings(use_api_llms=True, max_daily_api_cost=10.0)
    
    # Example 3: Adjust confidence
    print("\n3️⃣ ADJUSTING CONFIDENCE THRESHOLD:")
    control.change_settings(min_confidence_threshold=0.90)
    
    # Test decision making
    print("\n\n🧪 TESTING DECISION SYSTEM:")
    print("-"*70)
    
    # Test market data
    test_data = {
        'pair': 'EURUSD',
        'price': 1.0950,
        'rsi': 65,
        'trend': 'bullish'
    }
    
    # Make decision
    decision = await control.make_decision(test_data)
    
    print(f"\n📋 FINAL DECISION:")
    print(f"- Action: {decision['decision']}")
    print(f"- Confidence: {decision['confidence']:.1%}")
    print(f"- Source: {decision['source']}")
    print(f"- Components used: {decision['components_used']}")
    
    # Show final status
    control.show_status()
    
    print("\n\n💡 YOU CAN NOW:")
    print("- Enable/disable any component")
    print("- Set cost limits")
    print("- Adjust confidence thresholds")
    print("- Monitor everything in real-time!")

if __name__ == "__main__":
    asyncio.run(demo_control_system()) 