#!/usr/bin/env python
"""
ML Strategy Adapter for NORYONAI Backtesting Framework

Bridges trained ML models with the backtesting system for performance validation.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from backtesting_framework import Strategy, BacktestEngine, BacktestConfig, BacktestResult
from training.safe_training_system import SafeTrainingSystem
from training.real_data_loader import RealTradingDataLoader
import pickle
import os

class MLModelStrategy(Strategy):
    """Strategy that uses trained ML models for signal generation"""
    
    def __init__(self, model_path: str = "models/command_center", confidence_threshold: float = 0.7):
        super().__init__("ML Model Ensemble")
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.models = {}
        self.scaler = None
        self.symbol_encodings = {}
        self.load_models()
        
    def load_models(self):
        """Load trained ML models"""
        print(f"Loading ML models from {self.model_path}...")
        
        model_names = ['RandomForest', 'XGBoost', 'LightGBM', 'LogisticRegression']
        
        for name in model_names:
            model_file = os.path.join(self.model_path, f"{name}.pkl")
            if os.path.exists(model_file):
                with open(model_file, 'rb') as f:
                    self.models[name] = pickle.load(f)
                    
        # Load scaler
        scaler_file = os.path.join(self.model_path, "scaler.pkl")
        if os.path.exists(scaler_file):
            with open(scaler_file, 'rb') as f:
                self.scaler = pickle.load(f)
                
        # Load encodings
        encoding_file = os.path.join(self.model_path, "encodings.json")
        if os.path.exists(encoding_file):
            import json
            with open(encoding_file, 'r') as f:
                self.symbol_encodings = json.load(f)
                
        print(f"✅ Loaded {len(self.models)} ML models")
        
    def calculate_indicators(self, data: pd.DataFrame) -> None:
        """Calculate technical indicators needed for ML features"""
        
        # Ensure required columns exist
        if 'close' not in data.columns:
            if 'adj_close' in data.columns:
                data['close'] = data['adj_close']
            else:
                raise ValueError("No 'close' or 'adj_close' column found in data")
                
        # Price features
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # Handle missing high/low/open columns
        if 'high' not in data.columns:
            data['high'] = data['close'] * 1.001
        if 'low' not in data.columns:
            data['low'] = data['close'] * 0.999
        if 'open' not in data.columns:
            data['open'] = data['close'].shift(1).fillna(data['close'])
        if 'volume' not in data.columns:
            data['volume'] = 1000000  # Default volume
            
        data['high_low_ratio'] = data['high'] / data['low']
        data['close_open_ratio'] = data['close'] / data['open']
        
        # Volume features
        data['volume_ma_5'] = data['volume'].rolling(window=5).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma_5']
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            data[f'sma_{window}'] = data['close'].rolling(window=window).mean()
            data[f'price_to_sma_{window}'] = data['close'] / data[f'sma_{window}']
            
        # MACD
        data['ema_12'] = data['close'].ewm(span=12).mean()
        data['ema_26'] = data['close'].ewm(span=26).mean()
        data['macd'] = data['ema_12'] - data['ema_26']
        data['macd_signal'] = data['macd'].ewm(span=9).mean()
        data['macd_histogram'] = data['macd'] - data['macd_signal']
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        data['bb_middle'] = data['close'].rolling(window=20).mean()
        bb_std = data['close'].rolling(window=20).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # ATR
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        data['atr'] = true_range.rolling(window=14).mean()
        
        # Support/Resistance
        data['resistance_20'] = data['high'].rolling(window=20).max()
        data['support_20'] = data['low'].rolling(window=20).min()
        data['price_position'] = (data['close'] - data['support_20']) / (data['resistance_20'] - data['support_20'])
        
        # Time features
        if data.index.dtype == 'datetime64[ns]':
            data['hour'] = data.index.hour
            data['day_of_week'] = data.index.dayofweek
        else:
            data['hour'] = 12  # Default to noon
            data['day_of_week'] = 1  # Default to Tuesday
            
        data['is_market_open'] = data['hour'].between(9, 16)
        
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate signals using ML models"""
        
        if not self.models:
            print("⚠️ No ML models loaded, returning neutral signals")
            return pd.Series(index=data.index, data=0)
            
        signals = pd.Series(index=data.index, data=0)
        
        # Feature columns (matching training data)
        feature_columns = [
            'returns', 'log_returns', 'high_low_ratio', 'close_open_ratio',
            'volume_ratio', 'price_to_sma_5', 'price_to_sma_10', 'price_to_sma_20',
            'price_to_sma_50', 'macd', 'macd_signal', 'macd_histogram',
            'rsi', 'bb_position', 'atr', 'price_position',
            'hour', 'day_of_week', 'is_market_open'
        ]
        
        # Add symbol encoding (default to 0 if not found)
        data['symbol_encoded'] = 0
        feature_columns.append('symbol_encoded')
        
        # Generate signals for each row
        for i in range(50, len(data)):  # Skip first 50 rows for indicators
            try:
                # Extract features for current row
                current_features = []
                for col in feature_columns:
                    if col in data.columns:
                        value = data[col].iloc[i]
                        if pd.isna(value):
                            value = 0.0
                        current_features.append(float(value))
                    else:
                        current_features.append(0.0)
                        
                features = np.array([current_features])
                
                # Scale features if scaler is available
                if self.scaler is not None:
                    features = self.scaler.transform(features)
                
                # Get predictions from all models
                predictions = {}
                for name, model in self.models.items():
                    try:
                        pred = model.predict(features)[0]
                        prob = model.predict_proba(features)[0] if hasattr(model, 'predict_proba') else [0.5, 0.5]
                        predictions[name] = {
                            'prediction': int(pred),
                            'confidence': float(max(prob))
                        }
                    except Exception as e:
                        continue
                
                # Ensemble voting
                if predictions:
                    buy_votes = sum(1 for p in predictions.values() if p['prediction'] == 1)
                    sell_votes = len(predictions) - buy_votes
                    
                    # Calculate average confidence
                    if buy_votes > sell_votes:
                        avg_confidence = np.mean([p['confidence'] for p in predictions.values() 
                                                if p['prediction'] == 1])
                        if avg_confidence > self.confidence_threshold:
                            signals.iloc[i] = 1  # Buy signal
                    elif sell_votes > buy_votes:
                        avg_confidence = np.mean([p['confidence'] for p in predictions.values() 
                                                if p['prediction'] == 0])
                        if avg_confidence > self.confidence_threshold:
                            signals.iloc[i] = -1  # Sell signal
                            
            except Exception as e:
                continue
                
        return signals

class MLBacktestRunner:
    """Utility class to run ML model backtests"""
    
    def __init__(self, model_path: str = "models/command_center"):
        self.model_path = model_path
        
    def run_ml_backtest(self, data: pd.DataFrame, 
                       initial_capital: float = 100000,
                       confidence_threshold: float = 0.7,
                       risk_per_trade: float = 0.02) -> BacktestResult:
        """Run backtest using ML models"""
        
        print("🧪 Running ML Model Backtest...")
        print(f"Data shape: {data.shape}")
        print(f"Date range: {data.index.min()} to {data.index.max()}")
        
        # Create ML strategy
        strategy = MLModelStrategy(
            model_path=self.model_path,
            confidence_threshold=confidence_threshold
        )
        
        # Configure backtest
        config = BacktestConfig(
            initial_capital=initial_capital,
            commission=0.0002,  # 2 pips
            slippage=0.0001,    # 1 pip
            max_positions=5,
            risk_per_trade=risk_per_trade
        )
        
        # Run backtest
        engine = BacktestEngine(config)
        result = engine.run_backtest(data, strategy)
        
        return result
        
    def compare_ml_vs_traditional(self, data: pd.DataFrame) -> Dict[str, BacktestResult]:
        """Compare ML models against traditional strategies"""
        
        from backtesting_framework import TrendFollowingStrategy, MeanReversionStrategy
        
        results = {}
        
        # Test ML strategy
        print("Testing ML Model Strategy...")
        results['ML_Models'] = self.run_ml_backtest(data)
        
        # Test traditional strategies
        config = BacktestConfig(initial_capital=100000)
        engine = BacktestEngine(config)
        
        print("Testing Trend Following Strategy...")
        trend_strategy = TrendFollowingStrategy()
        results['Trend_Following'] = engine.run_backtest(data, trend_strategy)
        
        print("Testing Mean Reversion Strategy...")
        mean_strategy = MeanReversionStrategy()
        results['Mean_Reversion'] = engine.run_backtest(data, mean_strategy)
        
        return results

def demo_ml_backtesting():
    """Demonstrate ML model backtesting"""
    
    print("=" * 60)
    print("NORYONAI ML MODEL BACKTESTING DEMO")
    print("=" * 60)
    
    # Check if models exist
    model_path = "models/command_center"
    if not os.path.exists(model_path):
        print("❌ No trained models found!")
        print("Please train models first using the command center.")
        return
        
    # Generate sample data for testing
    from backtesting_framework import generate_sample_data
    data = generate_sample_data(days=252)
    
    # Run ML backtest
    runner = MLBacktestRunner(model_path)
    
    try:
        # Single ML backtest
        ml_result = runner.run_ml_backtest(data, confidence_threshold=0.7)
        
        # Print results
        from backtesting_framework import BacktestAnalyzer
        analyzer = BacktestAnalyzer()
        analyzer.print_results(ml_result, "ML Model Ensemble")
        
        # Compare strategies
        print("\n" + "=" * 60)
        print("STRATEGY COMPARISON")
        print("=" * 60)
        
        comparison_results = runner.compare_ml_vs_traditional(data)
        
        # Print comparison table
        comparison_data = []
        for name, result in comparison_results.items():
            comparison_data.append({
                'Strategy': name.replace('_', ' '),
                'Total Return': f"{result.total_return:.1%}",
                'Win Rate': f"{result.win_rate:.1%}",
                'Sharpe Ratio': f"{result.sharpe_ratio:.2f}",
                'Max Drawdown': f"{result.max_drawdown:.1%}",
                'Total Trades': result.total_trades
            })
            
        comparison_df = pd.DataFrame(comparison_data)
        print("\n" + comparison_df.to_string(index=False))
        
        # Save results
        print("\n💾 Saving ML backtest results...")
        import json
        
        ml_results_summary = {
            'total_return': ml_result.total_return,
            'win_rate': ml_result.win_rate,
            'sharpe_ratio': ml_result.sharpe_ratio,
            'max_drawdown': ml_result.max_drawdown,
            'total_trades': ml_result.total_trades,
            'profit_factor': ml_result.profit_factor
        }
        
        with open('ml_backtest_results.json', 'w') as f:
            json.dump(ml_results_summary, f, indent=2)
            
        print("✅ Results saved to ml_backtest_results.json")
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_ml_backtesting() 