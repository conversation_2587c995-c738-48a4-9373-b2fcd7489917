#!/usr/bin/env python
"""
NORYONAI Training Starter

Quick start script for beginning model training with optimal settings.
"""

import os
import sys
from datetime import datetime

def main():
    print("🚀 NORYONAI TRAINING STARTER")
    print("=" * 60)
    print(f"Starting: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("🎯 RECOMMENDED TRAINING SEQUENCE:")
    print()
    
    print("1️⃣ QUICK TEST TRAINING (Recommended First)")
    print("   • Sample Size: 10,000 samples")
    print("   • Duration: ~2-3 minutes")
    print("   • Purpose: Verify everything works")
    print("   Command: python noryonai_command_center.py")
    print("   Then select: 1 → 3 (Quick Training)")
    print()
    
    print("2️⃣ MEDIUM TRAINING")
    print("   • Sample Size: 100,000 samples") 
    print("   • Duration: ~10-15 minutes")
    print("   • Purpose: Good baseline models")
    print("   Command: python noryonai_command_center.py")
    print("   Then select: 1 → 2 (Train on Sample Data)")
    print()
    
    print("3️⃣ FULL TRAINING")
    print("   • Sample Size: 500,000 samples")
    print("   • Duration: ~30-45 minutes")
    print("   • Purpose: Production-ready models")
    print("   Command: python noryonai_command_center.py")
    print("   Then select: 1 → 1 (Train on Real Data)")
    print()
    
    print("📊 AFTER TRAINING:")
    print("   • Test with: python ml_strategy_adapter.py")
    print("   • Monitor with: python model_performance_monitor.py")
    print("   • Validate with: python test_ml_strategy_adapter.py")
    print()
    
    print("🎯 READY TO START!")
    print("✅ All systems tested and operational")
    print("✅ 2.8GB+ of quality data available")
    print("✅ Advanced feature engineering ready")
    print()
    
    choice = input("Start training now? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        print("\n🚀 Launching NORYONAI Command Center...")
        print("Select option 1 (Train Models) when it loads!")
        print()
        
        # Launch command center
        os.system("python noryonai_command_center.py")
    else:
        print("\n👍 Ready when you are!")
        print("Run: python noryonai_command_center.py")
        print("Then select option 1 to begin training.")

if __name__ == "__main__":
    main() 