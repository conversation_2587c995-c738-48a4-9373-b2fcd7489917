#!/usr/bin/env python3
"""
Comprehensive AI Model Testing and Simulation Runner
Executes model validation, performance testing, and trading simulations.
"""

import os
import sys
import time
import asyncio
from pathlib import Path
from datetime import datetime
import json
import traceback

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from core.model_testing_framework import model_testing_framework
from core.trading_simulator import trading_simulator
from core.logging_system import setup_logging, get_logger, get_performance_logger
from core.performance_monitor import performance_monitor
from core.model_manager import model_manager


class AIModelTestRunner:
    """
    Comprehensive AI model testing and simulation orchestrator.
    """
    
    def __init__(self):
        self.start_time = datetime.now()
        self.logger = None
        self.performance_logger = None
        self.results = {
            'timestamp': self.start_time.isoformat(),
            'model_testing': {},
            'trading_simulations': {},
            'performance_analysis': {},
            'summary': {}
        }
    
    def initialize_systems(self):
        """Initialize all required systems"""
        print("🚀 Initializing AI Model Testing Systems...")
        
        try:
            # Setup logging
            setup_logging()
            self.logger = get_logger(__name__)
            self.performance_logger = get_performance_logger()
            
            # Start performance monitoring
            performance_monitor.start()
            
            self.logger.info("AI Model Testing Runner initialized")
            print("✅ Systems initialized successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize systems: {e}")
            return False
    
    def discover_and_validate_models(self):
        """Discover and validate all available models"""
        print("\n🔍 Discovering and Validating Models...")
        
        try:
            # Discover models
            models = model_testing_framework.discover_models()
            
            total_models = sum(len(model_list) for model_list in models.values())
            print(f"📊 Model Discovery Results:")
            print(f"  • Total Models Found: {total_models}")
            
            for category, model_list in models.items():
                if model_list:
                    print(f"  • {category.title()}: {len(model_list)} models")
            
            # Validate model health
            model_health = model_manager.health_check()
            print(f"  • Model Manager Status: {model_health['status']}")
            
            self.results['model_discovery'] = {
                'total_models': total_models,
                'by_category': {cat: len(models) for cat, models in models.items()},
                'model_manager_health': model_health
            }
            
            return models
            
        except Exception as e:
            self.logger.error(f"Model discovery failed: {e}")
            print(f"❌ Model discovery failed: {e}")
            return {}
    
    def run_model_performance_tests(self, models: dict):
        """Run comprehensive model performance tests"""
        print("\n🧪 Running Model Performance Tests...")
        
        try:
            # Run all model tests
            with self.performance_logger.timer("comprehensive_model_testing"):
                test_results = model_testing_framework.test_all_models()
            
            # Generate test report
            test_report = model_testing_framework.generate_test_report()
            
            # Display results
            print(f"📊 Model Testing Results:")
            print(f"  • Total Tests: {test_report['summary']['total_tests']}")
            print(f"  • Passed: {test_report['summary']['passed_tests']}")
            print(f"  • Failed: {test_report['summary']['failed_tests']}")
            print(f"  • Success Rate: {test_report['summary']['success_rate']:.1%}")
            print(f"  • Average Accuracy: {test_report['summary']['avg_accuracy']:.3f}")
            print(f"  • Average Speed: {test_report['summary']['avg_predictions_per_second']:.1f} pred/sec")
            
            # Show top performers
            if test_report['top_performers']:
                print(f"\n🏆 Top Performing Models:")
                for i, model in enumerate(test_report['top_performers'][:3], 1):
                    print(f"  {i}. {model.model_name}: {model.accuracy:.3f} accuracy, {model.predictions_per_second:.1f} pred/sec")
            
            # Show failed models
            if test_report['failed_models']:
                print(f"\n❌ Failed Models ({len(test_report['failed_models'])}):")
                for model in test_report['failed_models'][:3]:
                    error = model.details.get('error', 'Unknown error')
                    print(f"  • {model.model_name}: {error}")
            
            # Save detailed results
            model_testing_framework.save_test_results()
            
            self.results['model_testing'] = test_report
            return test_results
            
        except Exception as e:
            self.logger.error(f"Model testing failed: {e}")
            print(f"❌ Model testing failed: {e}")
            traceback.print_exc()
            return {}
    
    def run_trading_simulations(self, models: dict):
        """Run trading simulations for financial models"""
        print("\n💹 Running Trading Simulations...")
        
        try:
            # Get financial models
            financial_models = models.get('financial', [])
            
            if not financial_models:
                print("⚠️ No financial models found for trading simulation")
                return {}
            
            print(f"🎯 Testing {len(financial_models)} financial models")
            
            # Run batch simulations
            with self.performance_logger.timer("trading_simulations"):
                simulation_results = trading_simulator.run_batch_simulations(
                    financial_models[:5],  # Limit to first 5 models for demo
                    simulation_days=7  # 1 week simulation
                )
            
            # Display results
            print(f"📊 Trading Simulation Results:")
            print(f"  • Total Simulations: {simulation_results['total_simulations']}")
            print(f"  • Successful: {simulation_results['successful_simulations']}")
            print(f"  • Failed: {simulation_results['failed_simulations']}")
            
            if simulation_results['successful_simulations'] > 0:
                print(f"  • Average Return: {simulation_results['avg_return']:.2%}")
                
                if simulation_results['best_model']:
                    best = simulation_results['best_model']
                    print(f"  • Best Model: {best['total_return']:.2%} return, {best['win_rate']:.1%} win rate")
                
                if simulation_results['worst_model']:
                    worst = simulation_results['worst_model']
                    print(f"  • Worst Model: {worst['total_return']:.2%} return, {worst['win_rate']:.1%} win rate")
            
            # Save simulation results
            trading_simulator.save_simulation_results()
            
            self.results['trading_simulations'] = simulation_results
            return simulation_results
            
        except Exception as e:
            self.logger.error(f"Trading simulation failed: {e}")
            print(f"❌ Trading simulation failed: {e}")
            traceback.print_exc()
            return {}
    
    def analyze_system_performance(self):
        """Analyze overall system performance"""
        print("\n📈 Analyzing System Performance...")
        
        try:
            # Get performance metrics
            health_status = performance_monitor.get_health_status()
            
            print(f"🔍 System Performance Analysis:")
            print(f"  • Overall Status: {health_status['status'].upper()}")
            
            # System metrics
            if 'system_metrics' in health_status:
                metrics = health_status['system_metrics']
                print(f"  • CPU Usage: {metrics.get('system_cpu_percent', 0):.1f}%")
                print(f"  • Memory Usage: {metrics.get('system_memory_percent', 0):.1f}%")
                print(f"  • Process Memory: {metrics.get('process_memory_mb', 0):.1f} MB")
            
            # Application metrics
            if 'application_metrics' in health_status:
                app_metrics = health_status['application_metrics']
                print(f"  • Average Response Time: {app_metrics.get('avg_response_time', 0):.3f}s")
                print(f"  • Error Rate: {app_metrics.get('error_rate', 0):.2%}")
            
            # Alerts
            if health_status.get('alerts'):
                print(f"  • Active Alerts: {len(health_status['alerts'])}")
                for alert in health_status['alerts'][:3]:
                    print(f"    - {alert['severity'].upper()}: {alert['message']}")
            
            self.results['performance_analysis'] = health_status
            return health_status
            
        except Exception as e:
            self.logger.error(f"Performance analysis failed: {e}")
            print(f"❌ Performance analysis failed: {e}")
            return {}
    
    def generate_comprehensive_report(self):
        """Generate comprehensive testing report"""
        print("\n📋 Generating Comprehensive Report...")
        
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        # Calculate summary statistics
        model_tests = self.results.get('model_testing', {}).get('summary', {})
        trading_sims = self.results.get('trading_simulations', {})
        
        summary = {
            'execution_time_minutes': total_duration / 60,
            'total_models_tested': model_tests.get('total_tests', 0),
            'model_success_rate': model_tests.get('success_rate', 0),
            'avg_model_accuracy': model_tests.get('avg_accuracy', 0),
            'trading_simulations_run': trading_sims.get('total_simulations', 0),
            'avg_trading_return': trading_sims.get('avg_return', 0),
            'system_status': self.results.get('performance_analysis', {}).get('status', 'unknown')
        }
        
        self.results['summary'] = summary
        
        # Save comprehensive report
        report_file = f"AI_MODEL_TESTING_REPORT_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # Display summary
        print(f"📊 COMPREHENSIVE TESTING SUMMARY:")
        print(f"  • Execution Time: {summary['execution_time_minutes']:.1f} minutes")
        print(f"  • Models Tested: {summary['total_models_tested']}")
        print(f"  • Model Success Rate: {summary['model_success_rate']:.1%}")
        print(f"  • Average Model Accuracy: {summary['avg_model_accuracy']:.3f}")
        print(f"  • Trading Simulations: {summary['trading_simulations_run']}")
        print(f"  • Average Trading Return: {summary['avg_trading_return']:.2%}")
        print(f"  • System Status: {summary['system_status'].upper()}")
        print(f"  • Detailed Report: {report_file}")
        
        return summary
    
    def run_comprehensive_testing(self):
        """Run all testing phases"""
        print("🚀 COMPREHENSIVE AI MODEL TESTING & SIMULATION")
        print("=" * 70)
        
        # Phase 1: Initialize systems
        if not self.initialize_systems():
            return False
        
        # Phase 2: Discover models
        models = self.discover_and_validate_models()
        if not models:
            print("❌ No models found. Exiting.")
            return False
        
        # Phase 3: Run model performance tests
        test_results = self.run_model_performance_tests(models)
        
        # Phase 4: Run trading simulations
        simulation_results = self.run_trading_simulations(models)
        
        # Phase 5: Analyze system performance
        performance_analysis = self.analyze_system_performance()
        
        # Phase 6: Generate comprehensive report
        summary = self.generate_comprehensive_report()
        
        # Final status
        print("\n" + "=" * 70)
        success_rate = summary.get('model_success_rate', 0)
        if success_rate >= 0.8:
            print("🎉 AI MODEL TESTING COMPLETED SUCCESSFULLY!")
            print("✅ Models are performing well and ready for production")
        elif success_rate >= 0.6:
            print("⚠️ AI MODEL TESTING COMPLETED WITH WARNINGS")
            print("🔧 Some models need attention before production deployment")
        else:
            print("❌ AI MODEL TESTING COMPLETED WITH ISSUES")
            print("🚨 Significant model issues detected - review required")
        
        # Stop performance monitoring
        performance_monitor.stop()
        
        return success_rate >= 0.6


def main():
    """Main testing function"""
    runner = AIModelTestRunner()
    success = runner.run_comprehensive_testing()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
