from training.real_data_loader import RealTradingDataLoader
import os

print("Testing data loading...")
print(f"Current directory: {os.getcwd()}")

# Check if data exists
data_path = "data/paperswithbacktestStocks-1Min-Price"
train_path = os.path.join(data_path, "train")

if os.path.exists(data_path):
    print(f"✓ Data directory exists: {data_path}")
else:
    print(f"✗ Data directory not found: {data_path}")

if os.path.exists(train_path):
    print(f"✓ Train directory exists: {train_path}")
    # List arrow files
    import glob
    arrow_files = glob.glob(os.path.join(train_path, "*.arrow"))
    print(f"✓ Found {len(arrow_files)} arrow files")
else:
    print(f"✗ Train directory not found: {train_path}")

# Try loading a small sample
try:
    loader = RealTradingDataLoader(data_path)
    print("\n✓ Data loader initialized")
    
    print("\nLoading 10,000 samples...")
    X, y = loader.load_and_prepare(sample_size=10000)
    
    print(f"\n✓ Successfully loaded data!")
    print(f"  - Features shape: {X.shape}")
    print(f"  - Targets shape: {y.shape}")
    print(f"  - Target distribution: Buy={sum(y==1)}, Sell={sum(y==0)}")
    
except Exception as e:
    print(f"\n✗ Error loading data: {e}")
    import traceback
    traceback.print_exc() 