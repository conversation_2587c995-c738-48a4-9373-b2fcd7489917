"""
Comprehensive Performance Monitoring System
Tracks system performance, resource usage, and business metrics.
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque, defaultdict
from contextlib import contextmanager

from core.exceptions import SystemError, handle_exception
from core.config_manager import config_manager
from core.logging_system import get_logger, get_performance_logger


@dataclass
class PerformanceMetric:
    """Performance metric data point"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class SystemResources:
    """System resource usage snapshot"""
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_available_gb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    timestamp: datetime


@dataclass
class ApplicationMetrics:
    """Application-specific metrics"""
    active_connections: int
    cache_hit_rate: float
    avg_response_time: float
    requests_per_second: float
    error_rate: float
    model_predictions_per_second: float
    timestamp: datetime


class MetricsCollector:
    """Collects and aggregates performance metrics"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self.lock = threading.Lock()
        self.logger = get_logger(__name__)
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None, unit: str = ""):
        """Record a performance metric"""
        with self.lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                timestamp=datetime.now(),
                tags=tags or {},
                unit=unit
            )
            self.metrics[name].append(metric)
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None):
        """Increment a counter metric"""
        with self.lock:
            self.counters[name] += value
            self.record_metric(f"{name}_total", self.counters[name], tags, "count")
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None, unit: str = ""):
        """Set a gauge metric"""
        with self.lock:
            self.gauges[name] = value
            self.record_metric(name, value, tags, unit)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a histogram value"""
        with self.lock:
            self.histograms[name].append(value)
            # Keep only recent values
            if len(self.histograms[name]) > self.max_history:
                self.histograms[name] = self.histograms[name][-self.max_history:]
            
            # Record percentiles
            if self.histograms[name]:
                sorted_values = sorted(self.histograms[name])
                count = len(sorted_values)
                
                percentiles = [50, 90, 95, 99]
                for p in percentiles:
                    index = int((p / 100.0) * count) - 1
                    if index >= 0:
                        self.record_metric(f"{name}_p{p}", sorted_values[index], tags, "seconds")
    
    def get_metrics(self, name: str, since: Optional[datetime] = None) -> List[PerformanceMetric]:
        """Get metrics for a specific name"""
        with self.lock:
            metrics = list(self.metrics.get(name, []))
            
            if since:
                metrics = [m for m in metrics if m.timestamp >= since]
            
            return metrics
    
    def get_summary(self) -> Dict[str, Any]:
        """Get metrics summary"""
        with self.lock:
            summary = {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histograms': {},
                'total_metrics': sum(len(deque_) for deque_ in self.metrics.values())
            }
            
            # Add histogram summaries
            for name, values in self.histograms.items():
                if values:
                    summary['histograms'][name] = {
                        'count': len(values),
                        'min': min(values),
                        'max': max(values),
                        'avg': sum(values) / len(values)
                    }
            
            return summary


class SystemMonitor:
    """Monitors system resource usage"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.logger = get_logger(__name__)
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 30.0):
        """Start system monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"System monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        self.logger.info("System monitoring stopped")
    
    def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                self._collect_system_metrics()
                time.sleep(interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """Collect system resource metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics_collector.set_gauge("system_cpu_percent", cpu_percent, unit="percent")
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.metrics_collector.set_gauge("system_memory_percent", memory.percent, unit="percent")
            self.metrics_collector.set_gauge("system_memory_used_gb", memory.used / (1024**3), unit="GB")
            self.metrics_collector.set_gauge("system_memory_available_gb", memory.available / (1024**3), unit="GB")
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.metrics_collector.set_gauge("system_disk_percent", disk_percent, unit="percent")
            self.metrics_collector.set_gauge("system_disk_free_gb", disk.free / (1024**3), unit="GB")
            
            # Network metrics
            network = psutil.net_io_counters()
            self.metrics_collector.set_gauge("system_network_bytes_sent", network.bytes_sent, unit="bytes")
            self.metrics_collector.set_gauge("system_network_bytes_recv", network.bytes_recv, unit="bytes")
            
            # Process metrics
            process = psutil.Process()
            self.metrics_collector.set_gauge("process_cpu_percent", process.cpu_percent(), unit="percent")
            self.metrics_collector.set_gauge("process_memory_mb", process.memory_info().rss / (1024**2), unit="MB")
            self.metrics_collector.set_gauge("process_threads", process.num_threads(), unit="count")
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")


class ApplicationMonitor:
    """Monitors application-specific metrics"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.logger = get_logger(__name__)
        
        # Request tracking
        self.request_times = deque(maxlen=1000)
        self.request_count = 0
        self.error_count = 0
        self.last_reset = time.time()
    
    @contextmanager
    def track_request(self, endpoint: str = "unknown"):
        """Context manager to track request performance"""
        start_time = time.time()
        error_occurred = False
        
        try:
            yield
        except Exception as e:
            error_occurred = True
            self.error_count += 1
            self.metrics_collector.increment_counter("http_errors", tags={"endpoint": endpoint})
            raise
        finally:
            duration = time.time() - start_time
            self.request_times.append(duration)
            self.request_count += 1
            
            # Record metrics
            self.metrics_collector.record_histogram("http_request_duration", duration, {"endpoint": endpoint})
            self.metrics_collector.increment_counter("http_requests", tags={"endpoint": endpoint})
            
            if error_occurred:
                self.metrics_collector.increment_counter("http_request_errors", tags={"endpoint": endpoint})
    
    def track_model_prediction(self, model_name: str, duration: float, success: bool = True):
        """Track model prediction performance"""
        tags = {"model": model_name, "status": "success" if success else "error"}
        
        self.metrics_collector.record_histogram("model_prediction_duration", duration, tags)
        self.metrics_collector.increment_counter("model_predictions", tags=tags)
        
        if not success:
            self.metrics_collector.increment_counter("model_prediction_errors", tags)
    
    def track_cache_operation(self, operation: str, hit: bool = True):
        """Track cache operation performance"""
        tags = {"operation": operation, "result": "hit" if hit else "miss"}
        self.metrics_collector.increment_counter("cache_operations", tags=tags)
    
    def get_current_metrics(self) -> ApplicationMetrics:
        """Get current application metrics"""
        now = time.time()
        time_window = now - self.last_reset
        
        # Calculate rates
        requests_per_second = self.request_count / time_window if time_window > 0 else 0
        error_rate = self.error_count / self.request_count if self.request_count > 0 else 0
        
        # Calculate average response time
        avg_response_time = sum(self.request_times) / len(self.request_times) if self.request_times else 0
        
        return ApplicationMetrics(
            active_connections=0,  # Would need to track separately
            cache_hit_rate=0.0,    # Would need to calculate from cache metrics
            avg_response_time=avg_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            model_predictions_per_second=0.0,  # Would need to track separately
            timestamp=datetime.now()
        )


class PerformanceMonitor:
    """
    Main performance monitoring system that coordinates all monitoring components.
    """
    
    def __init__(self):
        self.config = config_manager.get_section('monitoring')
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # Initialize components
        self.metrics_collector = MetricsCollector()
        self.system_monitor = SystemMonitor(self.metrics_collector)
        self.application_monitor = ApplicationMonitor(self.metrics_collector)
        
        # Monitoring state
        self.monitoring_enabled = getattr(self.config, 'enabled', True)
        self.collection_interval = getattr(self.config, 'metrics_collection_interval', 60)
        
        # Alert thresholds
        self.alert_thresholds = getattr(self.config, 'alert_thresholds', {})
    
    def start(self):
        """Start performance monitoring"""
        if not self.monitoring_enabled:
            self.logger.info("Performance monitoring disabled")
            return
        
        self.system_monitor.start_monitoring(self.collection_interval)
        self.logger.info("Performance monitoring started")
    
    def stop(self):
        """Stop performance monitoring"""
        self.system_monitor.stop_monitoring()
        self.logger.info("Performance monitoring stopped")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            # Get current metrics
            system_metrics = self._get_latest_system_metrics()
            app_metrics = self.application_monitor.get_current_metrics()
            
            # Check alert thresholds
            alerts = self._check_alert_thresholds(system_metrics)
            
            # Determine overall status
            status = "healthy"
            if any(alert['severity'] == 'critical' for alert in alerts):
                status = "critical"
            elif any(alert['severity'] == 'warning' for alert in alerts):
                status = "warning"
            
            return {
                'status': status,
                'timestamp': datetime.now().isoformat(),
                'system_metrics': system_metrics,
                'application_metrics': app_metrics.__dict__,
                'alerts': alerts,
                'metrics_summary': self.metrics_collector.get_summary()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get health status: {e}")
            return {
                'status': 'unknown',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _get_latest_system_metrics(self) -> Dict[str, float]:
        """Get latest system metrics"""
        metrics = {}
        
        metric_names = [
            'system_cpu_percent',
            'system_memory_percent',
            'system_disk_percent',
            'process_cpu_percent',
            'process_memory_mb'
        ]
        
        for name in metric_names:
            latest_metrics = self.metrics_collector.get_metrics(name)
            if latest_metrics:
                metrics[name] = latest_metrics[-1].value
        
        return metrics
    
    def _check_alert_thresholds(self, metrics: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check metrics against alert thresholds"""
        alerts = []
        
        threshold_checks = [
            ('system_cpu_percent', 'cpu_usage_percent', 'CPU usage high'),
            ('system_memory_percent', 'memory_usage_percent', 'Memory usage high'),
            ('system_disk_percent', 'disk_usage_percent', 'Disk usage high')
        ]
        
        for metric_name, threshold_key, message in threshold_checks:
            if metric_name in metrics and threshold_key in self.alert_thresholds:
                value = metrics[metric_name]
                threshold = self.alert_thresholds[threshold_key]
                
                if value > threshold:
                    severity = 'critical' if value > threshold * 1.1 else 'warning'
                    alerts.append({
                        'metric': metric_name,
                        'value': value,
                        'threshold': threshold,
                        'severity': severity,
                        'message': f"{message}: {value:.1f}% (threshold: {threshold}%)"
                    })
        
        return alerts
    
    @contextmanager
    def track_operation(self, operation_name: str, tags: Dict[str, str] = None):
        """Context manager to track operation performance"""
        start_time = time.time()
        
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.metrics_collector.record_histogram(
                f"operation_duration",
                duration,
                {**(tags or {}), "operation": operation_name}
            )
            self.metrics_collector.increment_counter(
                "operations",
                tags={**(tags or {}), "operation": operation_name}
            )


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
