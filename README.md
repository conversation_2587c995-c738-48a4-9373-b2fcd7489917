# 🚀 NoryonAI Trading System v2.0

An advanced AI-powered algorithmic trading system that combines machine learning with real-time market analysis across multiple asset classes.

## 📊 System Overview

- **85% Automated Trading**: Sophisticated algorithms handle most trading decisions
- **Multi-Asset Support**: Stocks, Crypto, Forex, and Commodities
- **Real-Time Processing**: 6.5M technical analysis points/second
- **AI-Powered Predictions**: Ensemble of 4 ML models (Random Forest, XGBoost, LightGBM, Logistic Regression)
- **1.2TB Historical Data**: Massive dataset for training and backtesting

## 🎯 Features

### Trading Agents
- **EquityAgent**: Stock trading with Elliott Wave patterns and sector rotation
- **CryptoAgent**: Cryptocurrency trading with on-chain analysis
- **ForexAgent**: Currency pair trading with correlation analysis
- **CommodityAgent**: Commodity futures and spot trading

### Technical Indicators
- Moving Averages (SMA, EMA)
- MACD (Moving Average Convergence Divergence)
- RSI (Relative Strength Index)
- Bollinger Bands
- ATR (Average True Range)
- Support/Resistance Levels
- Volume Analysis
- Elliott Wave Patterns

### Machine Learning Models
- **Random Forest**: For robust predictions
- **XGBoost**: For high-performance gradient boosting
- **LightGBM**: For fast, efficient predictions
- **Logistic Regression**: For baseline predictions
- **Ensemble Voting**: Combines all models for final signals

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Launch Command Center
```bash
python noryonai_command_center.py
```

### 3. Command Center Options

#### Main Menu:
1. **📚 Train Models** - Train ML models on historical data
2. **🤖 Initialize Agents** - Start trading agents
3. **📊 Generate Trading Signals** - Get real-time predictions
4. **💹 Start Auto Trading** - Enable automated trading
5. **📈 View Positions** - Monitor active trades
6. **📉 Market Analysis** - Deep market insights
7. **⚙️ System Configuration** - Adjust risk parameters
8. **💾 Save/Load Models** - Model persistence
9. **📋 View Logs** - System activity logs
0. **🚪 Exit** - Shutdown system

## 📁 Project Structure

```
noryonai-backend/
├── agents/                   # Trading agents for different markets
│   ├── EquityAgent.py       # Stock trading (2,809 lines)
│   ├── CryptoAgent.py       # Crypto trading (2,160 lines)
│   ├── ForexAgent.py        # Forex trading (1,125 lines)
│   └── CommodityAgent.py    # Commodity trading
├── training/                 # ML training systems
│   ├── safe_training_system.py   # PyTorch-free training
│   ├── real_data_loader.py      # Data loading & preprocessing
│   └── train_on_real_data.py    # Training orchestrator
├── models/                   # Saved ML models
│   ├── command_center/      # Command center models
│   └── real_data/           # Production models
├── data/                    # Trading data (1.2TB)
│   ├── paperswithbacktestStocks-1Min-Price/
│   ├── Kiceldaily-stocks/
│   └── SP500/
├── config.json              # System configuration
└── noryonai_command_center.py  # Main interface
```

## ⚙️ Configuration

Default configuration (config.json):
```json
{
    "risk_per_trade": 0.02,      // 2% risk per trade
    "max_positions": 10,         // Maximum concurrent positions
    "confidence_threshold": 0.7,  // 70% confidence required
    "data_update_interval": 5,    // Update every 5 seconds
    "auto_save_interval": 300     // Auto-save every 5 minutes
}
```

## 📊 Performance Metrics

- **Data Processing**: 3.9M records/second
- **Technical Analysis**: 6.5M points/second
- **Model Training**: Supports datasets up to 500k samples
- **Prediction Speed**: Real-time (<100ms per prediction)

## 🔧 API Integration

The system supports integration with:
- **Alpaca** (Stocks)
- **Binance** (Crypto)
- **OANDA** (Forex)
- **Interactive Brokers** (All markets)

## 🚨 Risk Management

- Position sizing based on Kelly Criterion
- Stop-loss and take-profit automation
- Maximum drawdown limits
- Portfolio diversification rules
- Real-time risk monitoring

## 📈 Advanced Features

### Elliott Wave Analysis
- Automatic wave pattern detection
- Fibonacci retracement levels
- Wave degree classification

### Market Microstructure
- Order flow analysis
- Level 2 data processing
- Dark pool detection

### Sentiment Analysis
- News sentiment scoring
- Social media monitoring
- Market fear/greed index

## 🛠️ Troubleshooting

### PyTorch Issues
The system now uses scikit-learn, XGBoost, and LightGBM instead of PyTorch for better stability.

### Data Loading
Ensure arrow files are in the correct directory structure as shown above.

### Memory Usage
For large datasets, use the `sample_size` parameter to limit data loading.

## 📝 License

Proprietary - All rights reserved

## 🤝 Support

For issues or questions, check the logs in the command center or contact support.

---

**Version**: 2.0  
**Status**: Production Ready  
**Last Updated**: December 2024