# CI/CD Pipeline for NoryonAI Backend
# Comprehensive pipeline with testing, security scanning, and deployment

name: NoryonAI CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: noryonai/backend

jobs:
  # Code Quality and Testing
  test:
    name: Test & Quality Checks
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov black flake8 mypy safety bandit
    
    - name: Code formatting check
      run: |
        black --check --diff .
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Type checking with mypy
      run: |
        mypy core/ --ignore-missing-imports
    
    - name: Security check with bandit
      run: |
        bandit -r core/ -f json -o bandit-report.json
    
    - name: Dependency vulnerability check
      run: |
        safety check --json --output safety-report.json
    
    - name: Run unit tests
      run: |
        python -m pytest tests/unit/ -v --cov=core --cov-report=xml --cov-report=html
    
    - name: Run integration tests
      run: |
        python -m pytest tests/integration/ -v
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
    
    - name: Upload test artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-reports
        path: |
          htmlcov/
          bandit-report.json
          safety-report.json

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Push Docker Image
  build:
    name: Build & Push Image
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: deployment/docker/Dockerfile
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}
        format: spdx-json
        output-file: sbom.spdx.json
    
    - name: Upload SBOM
      uses: actions/upload-artifact@v3
      with:
        name: sbom
        path: sbom.spdx.json

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}
    
    - name: Deploy to staging
      run: |
        # Update image tag in deployment
        sed -i "s|noryonai/backend:.*|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:develop|g" deployment/kubernetes/deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f deployment/kubernetes/namespace.yaml
        kubectl apply -f deployment/kubernetes/secrets.yaml
        kubectl apply -f deployment/kubernetes/configmap.yaml
        kubectl apply -f deployment/kubernetes/pvc.yaml
        kubectl apply -f deployment/kubernetes/deployment.yaml
        kubectl apply -f deployment/kubernetes/service.yaml
        kubectl apply -f deployment/kubernetes/ingress.yaml
        
        # Wait for rollout
        kubectl rollout status deployment/noryonai-backend -n noryonai --timeout=300s
    
    - name: Run smoke tests
      run: |
        # Wait for service to be ready
        sleep 30
        
        # Get service URL
        STAGING_URL=$(kubectl get ingress noryonai-ingress -n noryonai -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
        
        # Run smoke tests
        curl -f http://$STAGING_URL/health || exit 1
        curl -f http://$STAGING_URL/ready || exit 1

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
    
    - name: Deploy to production
      run: |
        # Update image tag in deployment
        sed -i "s|noryonai/backend:.*|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.event.release.tag_name }}|g" deployment/kubernetes/deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f deployment/kubernetes/namespace.yaml
        kubectl apply -f deployment/kubernetes/secrets.yaml
        kubectl apply -f deployment/kubernetes/configmap.yaml
        kubectl apply -f deployment/kubernetes/pvc.yaml
        kubectl apply -f deployment/kubernetes/deployment.yaml
        kubectl apply -f deployment/kubernetes/service.yaml
        kubectl apply -f deployment/kubernetes/ingress.yaml
        
        # Wait for rollout
        kubectl rollout status deployment/noryonai-backend -n noryonai --timeout=600s
    
    - name: Run production health checks
      run: |
        # Wait for service to be ready
        sleep 60
        
        # Get service URL
        PROD_URL=$(kubectl get ingress noryonai-ingress -n noryonai -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
        
        # Run comprehensive health checks
        curl -f https://$PROD_URL/health || exit 1
        curl -f https://$PROD_URL/ready || exit 1
        curl -f https://$PROD_URL/metrics || exit 1
    
    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 NoryonAI Backend ${{ github.event.release.tag_name }} deployed to production successfully!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run load tests
      run: |
        # Install k6
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
        
        # Run performance tests
        k6 run tests/performance/load_test.js
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json
