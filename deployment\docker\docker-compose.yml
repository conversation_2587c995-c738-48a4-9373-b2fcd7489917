# Docker Compose for NoryonAI Backend Stack
# Production-ready multi-service deployment

version: '3.8'

services:
  # Main NoryonAI Application
  noryonai-app:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
      target: production
    container_name: noryonai-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - NORYONAI_ENV=production
      - NORYONAI_DB_HOST=postgres
      - NORYONAI_DB_PASSWORD=${DB_PASSWORD}
      - NORYONAI_SECRET_KEY=${SECRET_KEY}
      - NORYONAI_REDIS_HOST=redis
    volumes:
      - ./data:/app/data:ro
      - ./models:/app/models:ro
      - ./logs:/app/logs
      - ./config:/app/config:ro
    depends_on:
      - postgres
      - redis
    networks:
      - noryonai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: noryonai-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=noryonai
      - POSTGRES_USER=noryonai_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - noryonai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U noryonai_user -d noryonai"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: noryonai-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - noryonai-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: noryonai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - noryonai-app
    networks:
      - noryonai-network

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: noryonai-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - noryonai-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: noryonai-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - noryonai-network

  # Log Aggregation - ELK Stack (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: noryonai-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - noryonai-network
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: noryonai-logstash
    restart: unless-stopped
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./logs:/app/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - noryonai-network
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: noryonai-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - noryonai-network
    profiles:
      - logging

# Networks
networks:
  noryonai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
