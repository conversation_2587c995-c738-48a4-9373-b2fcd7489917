#!/usr/bin/env python
"""
NORYONAI System Status Report

Comprehensive testing and status report for all system components.
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Any

def test_component(component_name: str, test_func) -> Dict[str, Any]:
    """Test a system component and return results"""
    
    print(f"\n🧪 Testing {component_name}...")
    
    try:
        result = test_func()
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}")
        return {
            'name': component_name,
            'status': 'PASS' if result else 'FAIL',
            'working': result,
            'error': None
        }
    except Exception as e:
        print(f"   ❌ FAIL - {str(e)}")
        return {
            'name': component_name,
            'status': 'FAIL',
            'working': False,
            'error': str(e)
        }

def test_data_quality_validator():
    """Test data quality validator"""
    try:
        from data_quality_validator import DataQualityValidator
        validator = DataQualityValidator()
        
        # Test with a simple CSV file
        test_result = validator.validate_dataset("data/sp500_news_290k_articles.csv")
        return test_result['overall_score'] > 0
    except:
        return False

def test_feature_engineering():
    """Test advanced feature engineering"""
    try:
        from advanced_feature_engineering import AdvancedFeatureEngineer
        import pandas as pd
        import numpy as np
        
        # Create sample data
        dates = pd.date_range('2023-01-01', '2023-01-31', freq='D')
        sample_data = pd.DataFrame({
            'open': np.random.randn(len(dates)) + 100,
            'high': np.random.randn(len(dates)) + 101,
            'low': np.random.randn(len(dates)) + 99,
            'close': np.random.randn(len(dates)) + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        engineer = AdvancedFeatureEngineer()
        features = engineer.create_comprehensive_features(sample_data, symbol="TEST")
        
        return len(features.columns) > 50  # Should create many features
    except:
        return False

def test_performance_monitor():
    """Test model performance monitor"""
    try:
        from model_performance_monitor import ModelPerformanceMonitor
        import numpy as np
        
        monitor = ModelPerformanceMonitor()
        
        # Test with sample data
        predictions = np.random.binomial(1, 0.7, 100)
        actual_values = np.random.binomial(1, 0.5, 100)
        features = np.random.randn(100, 10)
        
        result = monitor.monitor_model_performance(
            "TestModel", predictions, actual_values, features
        )
        
        return 'metrics' in result and 'alerts' in result
    except:
        return False

def test_ml_strategy_adapter():
    """Test ML strategy adapter"""
    try:
        from ml_strategy_adapter import MLModelStrategy, MLBacktestRunner
        
        # Test strategy initialization
        strategy = MLModelStrategy(model_path="nonexistent", confidence_threshold=0.7)
        
        # Test runner initialization
        runner = MLBacktestRunner(model_path="nonexistent")
        
        return True
    except:
        return False

def test_backtesting_framework():
    """Test backtesting framework"""
    try:
        from backtesting_framework import BacktestConfig, BacktestEngine
        
        config = BacktestConfig(initial_capital=100000)
        engine = BacktestEngine(config)
        
        return True
    except:
        return False

def test_command_center():
    """Test command center"""
    try:
        from noryonai_command_center import NoryonAICommandCenter
        
        # Just test initialization
        center = NoryonAICommandCenter()
        
        return hasattr(center, 'models') and hasattr(center, 'config')
    except:
        return False

def test_training_system():
    """Test training system"""
    try:
        from training.safe_training_system import SafeTrainingSystem
        from training.real_data_loader import RealTradingDataLoader
        
        trainer = SafeTrainingSystem()
        
        return hasattr(trainer, 'models') and hasattr(trainer, 'train_ensemble')
    except:
        return False

def check_data_availability():
    """Check data availability"""
    
    data_status = {}
    
    # Priority datasets
    datasets = [
        "data/paperswithbacktestStocks-1Min-Price",
        "data/sp500_news_290k_articles.csv",
        "data/pmoe7SP_500_Stocks_Data-ratios_news_price_10_yrs",
        "data/paperswithbacktestForex-Daily-Price",
        "data/paperswithbacktestETFs-Daily-Price"
    ]
    
    for dataset in datasets:
        name = os.path.basename(dataset)
        exists = os.path.exists(dataset)
        
        if exists:
            if os.path.isfile(dataset):
                size_mb = os.path.getsize(dataset) / (1024 * 1024)
            else:
                # Directory - calculate total size
                total_size = 0
                for root, dirs, files in os.walk(dataset):
                    for file in files:
                        try:
                            total_size += os.path.getsize(os.path.join(root, file))
                        except:
                            pass
                size_mb = total_size / (1024 * 1024)
        else:
            size_mb = 0
            
        data_status[name] = {
            'exists': exists,
            'size_mb': round(size_mb, 2),
            'status': '✅ Available' if exists else '❌ Missing'
        }
    
    return data_status

def check_dependencies():
    """Check Python dependencies"""
    
    required_packages = [
        'pandas', 'numpy', 'sklearn', 'xgboost', 'lightgbm',
        'pyarrow', 'colorama', 'warnings'
    ]
    
    dependency_status = {}
    
    for package in required_packages:
        try:
            __import__(package)
            dependency_status[package] = '✅ Installed'
        except ImportError:
            dependency_status[package] = '❌ Missing'
    
    return dependency_status

def generate_system_report():
    """Generate comprehensive system status report"""
    
    print("🚀 NORYONAI SYSTEM STATUS REPORT")
    print("=" * 80)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Test all components
    components = [
        ("Data Quality Validator", test_data_quality_validator),
        ("Advanced Feature Engineering", test_feature_engineering),
        ("Model Performance Monitor", test_performance_monitor),
        ("ML Strategy Adapter", test_ml_strategy_adapter),
        ("Backtesting Framework", test_backtesting_framework),
        ("Command Center", test_command_center),
        ("Training System", test_training_system),
    ]
    
    component_results = []
    for name, test_func in components:
        result = test_component(name, test_func)
        component_results.append(result)
    
    # Check data availability
    print("\n📊 DATA AVAILABILITY:")
    print("-" * 40)
    data_status = check_data_availability()
    for name, status in data_status.items():
        print(f"   {status['status']} {name} ({status['size_mb']} MB)")
    
    # Check dependencies
    print("\n📦 DEPENDENCIES:")
    print("-" * 40)
    deps = check_dependencies()
    for package, status in deps.items():
        print(f"   {status} {package}")
    
    # Summary
    print("\n📋 COMPONENT SUMMARY:")
    print("-" * 40)
    working_count = sum(1 for r in component_results if r['working'])
    total_count = len(component_results)
    
    for result in component_results:
        status_icon = "✅" if result['working'] else "❌"
        print(f"   {status_icon} {result['name']}")
        if result['error']:
            print(f"      Error: {result['error']}")
    
    # Overall status
    print(f"\n🎯 OVERALL STATUS:")
    print("-" * 40)
    print(f"   Components Working: {working_count}/{total_count} ({working_count/total_count*100:.1f}%)")
    
    available_data = sum(1 for d in data_status.values() if d['exists'])
    total_data = len(data_status)
    print(f"   Data Available: {available_data}/{total_data} ({available_data/total_data*100:.1f}%)")
    
    missing_deps = sum(1 for d in deps.values() if 'Missing' in d)
    total_deps = len(deps)
    print(f"   Dependencies: {total_deps-missing_deps}/{total_deps} ({(total_deps-missing_deps)/total_deps*100:.1f}%)")
    
    # Readiness assessment
    print(f"\n🚦 READINESS ASSESSMENT:")
    print("-" * 40)
    
    if working_count >= 6 and available_data >= 2:
        print("   🟢 READY FOR TRAINING")
        print("   ✅ Core systems operational")
        print("   ✅ Sufficient data available")
        print("   🚀 You can proceed with model training!")
    elif working_count >= 4:
        print("   🟡 MOSTLY READY")
        print("   ⚠️ Some components need attention")
        print("   🔧 Fix remaining issues before training")
    else:
        print("   🔴 NOT READY")
        print("   ❌ Critical components failing")
        print("   🛠️ Significant fixes needed")
    
    # Save report
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'components': component_results,
        'data_status': data_status,
        'dependencies': deps,
        'summary': {
            'components_working': working_count,
            'total_components': total_count,
            'data_available': available_data,
            'total_datasets': total_data,
            'dependencies_ok': total_deps - missing_deps,
            'total_dependencies': total_deps
        }
    }
    
    # Convert boolean values to strings for JSON serialization
    def convert_bools(obj):
        if isinstance(obj, dict):
            return {k: convert_bools(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_bools(item) for item in obj]
        elif isinstance(obj, bool):
            return str(obj)
        else:
            return obj
    
    report_data = convert_bools(report_data)
    
    with open('system_status_report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\n💾 Report saved to: system_status_report.json")
    
    return report_data

if __name__ == "__main__":
    generate_system_report() 