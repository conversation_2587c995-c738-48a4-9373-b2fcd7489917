"""
Enhanced Caching System with Redis Support
Provides intelligent caching for models, data, and API responses.
"""

import json
import pickle
import hashlib
import time
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from abc import ABC, abstractmethod

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from core.exceptions import SystemError, handle_exception
from core.config_manager import config_manager
from core.logging_system import get_logger, get_performance_logger


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class CacheBackend(ABC):
    """Abstract cache backend interface"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """Clear all cache entries"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        pass


class MemoryCacheBackend(CacheBackend):
    """In-memory cache backend"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.logger = get_logger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from memory cache"""
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        
        # Check expiration
        if entry.expires_at and datetime.now() > entry.expires_at:
            del self.cache[key]
            return None
        
        # Update access statistics
        entry.access_count += 1
        entry.last_accessed = datetime.now()
        
        return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in memory cache"""
        try:
            # Calculate expiration
            expires_at = None
            if ttl or self.default_ttl:
                expires_at = datetime.now() + timedelta(seconds=ttl or self.default_ttl)
            
            # Calculate size
            size_bytes = len(pickle.dumps(value))
            
            # Check if we need to evict entries
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at,
                size_bytes=size_bytes
            )
            
            self.cache[key] = entry
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set cache entry {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from memory cache"""
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in memory cache"""
        return key in self.cache
    
    def clear(self) -> bool:
        """Clear all memory cache entries"""
        self.cache.clear()
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """Get memory cache statistics"""
        total_size = sum(entry.size_bytes for entry in self.cache.values())
        return {
            'backend': 'memory',
            'entries': len(self.cache),
            'max_size': self.max_size,
            'total_size_bytes': total_size,
            'hit_rate': self._calculate_hit_rate()
        }
    
    def _evict_lru(self):
        """Evict least recently used entry"""
        if not self.cache:
            return
        
        # Find LRU entry
        lru_key = min(
            self.cache.keys(),
            key=lambda k: self.cache[k].last_accessed or self.cache[k].created_at
        )
        
        del self.cache[lru_key]
        self.logger.debug(f"Evicted LRU cache entry: {lru_key}")
    
    def _calculate_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total_accesses = sum(entry.access_count for entry in self.cache.values())
        if total_accesses == 0:
            return 0.0
        return total_accesses / len(self.cache) if self.cache else 0.0


class RedisCacheBackend(CacheBackend):
    """Redis cache backend"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, 
                 db: int = 0, password: Optional[str] = None,
                 default_ttl: int = 3600):
        if not REDIS_AVAILABLE:
            raise SystemError("Redis not available. Install redis-py package.")
        
        self.default_ttl = default_ttl
        self.logger = get_logger(__name__)
        
        try:
            self.redis_client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=False,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True
            )
            
            # Test connection
            self.redis_client.ping()
            self.logger.info(f"Connected to Redis at {host}:{port}")
            
        except Exception as e:
            raise SystemError(
                f"Failed to connect to Redis: {e}",
                context={'host': host, 'port': port},
                recovery_suggestions=[
                    "Check Redis server is running",
                    "Verify connection parameters",
                    "Check network connectivity"
                ]
            )
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        try:
            data = self.redis_client.get(key)
            if data is None:
                return None
            
            # Deserialize the data
            return pickle.loads(data)
            
        except Exception as e:
            self.logger.error(f"Failed to get cache entry {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache"""
        try:
            # Serialize the data
            data = pickle.dumps(value)
            
            # Set with TTL
            return self.redis_client.setex(
                key, 
                ttl or self.default_ttl, 
                data
            )
            
        except Exception as e:
            self.logger.error(f"Failed to set cache entry {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from Redis cache"""
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            self.logger.error(f"Failed to delete cache entry {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in Redis cache"""
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            self.logger.error(f"Failed to check cache entry {key}: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all Redis cache entries"""
        try:
            return bool(self.redis_client.flushdb())
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics"""
        try:
            info = self.redis_client.info()
            return {
                'backend': 'redis',
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info)
            }
        except Exception as e:
            self.logger.error(f"Failed to get cache stats: {e}")
            return {'backend': 'redis', 'error': str(e)}
    
    def _calculate_hit_rate(self, info: Dict) -> float:
        """Calculate Redis hit rate"""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        total = hits + misses
        return hits / total if total > 0 else 0.0


class CacheManager:
    """
    Enhanced cache manager with multiple backends and intelligent caching strategies.
    """
    
    def __init__(self):
        self.config = config_manager.get_section('caching')
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # Initialize cache backend
        self.backend = self._initialize_backend()
        
        # Cache statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    @handle_exception
    def _initialize_backend(self) -> CacheBackend:
        """Initialize cache backend based on configuration"""
        backend_type = getattr(self.config, 'backend', 'memory')
        
        if backend_type == 'redis' and REDIS_AVAILABLE:
            redis_config = getattr(self.config, 'redis', {})
            return RedisCacheBackend(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                default_ttl=getattr(self.config, 'default_ttl', 3600)
            )
        else:
            return MemoryCacheBackend(
                max_size=getattr(self.config, 'max_size', 1000),
                default_ttl=getattr(self.config, 'default_ttl', 3600)
            )
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache with performance tracking"""
        cache_key = self._normalize_key(key)
        
        with self.performance_logger.timer("cache_get", key=cache_key):
            value = self.backend.get(cache_key)
            
            if value is not None:
                self.stats['hits'] += 1
                self.logger.debug(f"Cache hit: {cache_key}")
            else:
                self.stats['misses'] += 1
                self.logger.debug(f"Cache miss: {cache_key}")
            
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
        """Set value in cache with performance tracking"""
        cache_key = self._normalize_key(key)
        
        with self.performance_logger.timer("cache_set", key=cache_key):
            success = self.backend.set(cache_key, value, ttl)
            
            if success:
                self.stats['sets'] += 1
                self.logger.debug(f"Cache set: {cache_key}")
            else:
                self.stats['errors'] += 1
                self.logger.warning(f"Cache set failed: {cache_key}")
            
            return success
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        cache_key = self._normalize_key(key)
        
        success = self.backend.delete(cache_key)
        if success:
            self.stats['deletes'] += 1
            self.logger.debug(f"Cache delete: {cache_key}")
        
        return success
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        cache_key = self._normalize_key(key)
        return self.backend.exists(cache_key)
    
    def clear(self) -> bool:
        """Clear all cache entries"""
        success = self.backend.clear()
        if success:
            self.stats = {key: 0 for key in self.stats}
            self.logger.info("Cache cleared")
        return success
    
    def get_or_set(self, key: str, factory_func, ttl: Optional[int] = None) -> Any:
        """Get value from cache or set it using factory function"""
        value = self.get(key)
        if value is not None:
            return value
        
        # Generate value using factory function
        with self.performance_logger.timer("cache_factory", key=key):
            value = factory_func()
        
        # Cache the generated value
        self.set(key, value, ttl)
        return value
    
    def _normalize_key(self, key: str) -> str:
        """Normalize cache key"""
        # Add namespace prefix
        namespace = getattr(self.config, 'namespace', 'noryonai')
        return f"{namespace}:{key}"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        backend_stats = self.backend.get_stats()
        
        total_operations = sum(self.stats.values())
        hit_rate = self.stats['hits'] / (self.stats['hits'] + self.stats['misses']) if (self.stats['hits'] + self.stats['misses']) > 0 else 0
        
        return {
            'manager_stats': {
                **self.stats,
                'total_operations': total_operations,
                'hit_rate': hit_rate
            },
            'backend_stats': backend_stats
        }
    
    def health_check(self) -> Dict[str, Any]:
        """Perform cache health check"""
        try:
            # Test basic operations
            test_key = "health_check_test"
            test_value = {"timestamp": time.time()}
            
            # Test set
            set_success = self.set(test_key, test_value, ttl=60)
            
            # Test get
            get_value = self.get(test_key)
            get_success = get_value is not None
            
            # Test delete
            delete_success = self.delete(test_key)
            
            return {
                'status': 'healthy' if all([set_success, get_success, delete_success]) else 'unhealthy',
                'operations': {
                    'set': set_success,
                    'get': get_success,
                    'delete': delete_success
                },
                'backend': self.backend.__class__.__name__
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'backend': self.backend.__class__.__name__
            }


# Global cache manager instance
cache_manager = CacheManager()
