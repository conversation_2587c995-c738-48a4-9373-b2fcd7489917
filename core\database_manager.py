"""
Enhanced Database Management System
Provides connection pooling, query optimization, and performance monitoring.
"""

import time
import asyncio
from typing import Dict, List, Any, Optional, Union, Tuple
from contextlib import contextmanager, asynccontextmanager
from dataclasses import dataclass
from datetime import datetime

try:
    import psycopg2
    from psycopg2 import pool, sql
    from psycopg2.extras import RealDictCursor
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False

from core.exceptions import SystemError, DataError, handle_exception
from core.config_manager import config_manager
from core.logging_system import get_logger, get_performance_logger
from core.cache_manager import cache_manager


@dataclass
class QueryResult:
    """Query result with metadata"""
    rows: List[Dict[str, Any]]
    row_count: int
    execution_time: float
    query_hash: str
    cached: bool = False


@dataclass
class ConnectionStats:
    """Connection pool statistics"""
    total_connections: int
    active_connections: int
    idle_connections: int
    waiting_connections: int
    total_queries: int
    avg_query_time: float
    cache_hit_rate: float


class DatabaseManager:
    """
    Enhanced database manager with connection pooling and query optimization.
    """
    
    def __init__(self):
        self.config = config_manager.get_section('database')
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # Connection pool
        self.connection_pool = None
        self.async_pool = None
        
        # Query statistics
        self.query_stats = {
            'total_queries': 0,
            'total_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0,
            'slow_queries': 0
        }
        
        # Initialize connection pool
        self._initialize_connection_pool()
    
    @handle_exception
    def _initialize_connection_pool(self):
        """Initialize database connection pool"""
        if not PSYCOPG2_AVAILABLE:
            raise SystemError(
                "PostgreSQL driver not available. Install psycopg2-binary package.",
                recovery_suggestions=["pip install psycopg2-binary"]
            )
        
        try:
            # Create connection pool
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=self.config.pool_size,
                host=self.config.host,
                port=self.config.port,
                database=self.config.database,
                user=self.config.username,
                password=self.config.password,
                cursor_factory=RealDictCursor,
                connect_timeout=self.config.pool_timeout
            )
            
            self.logger.info(
                f"Database connection pool initialized: {self.config.host}:{self.config.port}/{self.config.database}"
            )
            
        except Exception as e:
            raise SystemError(
                f"Failed to initialize database connection pool: {e}",
                context={
                    'host': self.config.host,
                    'port': self.config.port,
                    'database': self.config.database
                },
                recovery_suggestions=[
                    "Check database server is running",
                    "Verify connection parameters",
                    "Check network connectivity",
                    "Verify database credentials"
                ]
            )
    
    @contextmanager
    def get_connection(self):
        """Get database connection from pool"""
        connection = None
        try:
            connection = self.connection_pool.getconn()
            if connection is None:
                raise DataError("Failed to get connection from pool")
            
            yield connection
            
        except Exception as e:
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                self.connection_pool.putconn(connection)
    
    @handle_exception
    def execute_query(self, query: str, params: Optional[Tuple] = None, 
                     cache_ttl: Optional[int] = None) -> QueryResult:
        """
        Execute SQL query with caching and performance monitoring.
        
        Args:
            query: SQL query string
            params: Query parameters
            cache_ttl: Cache TTL in seconds (None = no caching)
            
        Returns:
            QueryResult with rows and metadata
        """
        # Generate query hash for caching
        query_hash = self._generate_query_hash(query, params)
        
        # Check cache first
        if cache_ttl:
            cached_result = cache_manager.get(f"query:{query_hash}")
            if cached_result:
                self.query_stats['cache_hits'] += 1
                self.logger.debug(f"Query cache hit: {query_hash[:8]}")
                return QueryResult(
                    rows=cached_result['rows'],
                    row_count=cached_result['row_count'],
                    execution_time=cached_result['execution_time'],
                    query_hash=query_hash,
                    cached=True
                )
        
        # Execute query
        start_time = time.time()
        
        with self.performance_logger.timer("database_query", query_hash=query_hash[:8]):
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    try:
                        cursor.execute(query, params)
                        
                        # Fetch results for SELECT queries
                        if cursor.description:
                            rows = [dict(row) for row in cursor.fetchall()]
                            row_count = len(rows)
                        else:
                            rows = []
                            row_count = cursor.rowcount
                        
                        conn.commit()
                        
                    except Exception as e:
                        conn.rollback()
                        raise DataError(
                            f"Query execution failed: {e}",
                            context={
                                'query': query[:100] + '...' if len(query) > 100 else query,
                                'params': str(params)[:100] if params else None
                            },
                            recovery_suggestions=[
                                "Check SQL syntax",
                                "Verify table and column names",
                                "Check parameter types and values"
                            ]
                        )
        
        execution_time = time.time() - start_time
        
        # Update statistics
        self.query_stats['total_queries'] += 1
        self.query_stats['total_time'] += execution_time
        self.query_stats['cache_misses'] += 1
        
        if execution_time > 1.0:  # Slow query threshold
            self.query_stats['slow_queries'] += 1
            self.logger.warning(f"Slow query detected: {execution_time:.2f}s - {query[:100]}")
        
        # Create result
        result = QueryResult(
            rows=rows,
            row_count=row_count,
            execution_time=execution_time,
            query_hash=query_hash
        )
        
        # Cache result if requested
        if cache_ttl and rows:
            cache_data = {
                'rows': rows,
                'row_count': row_count,
                'execution_time': execution_time
            }
            cache_manager.set(f"query:{query_hash}", cache_data, ttl=cache_ttl)
        
        return result
    
    @handle_exception
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """
        Execute query with multiple parameter sets.
        
        Args:
            query: SQL query string
            params_list: List of parameter tuples
            
        Returns:
            Total number of affected rows
        """
        with self.performance_logger.timer("database_execute_many", batch_size=len(params_list)):
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    try:
                        cursor.executemany(query, params_list)
                        conn.commit()
                        return cursor.rowcount
                        
                    except Exception as e:
                        conn.rollback()
                        raise DataError(
                            f"Batch execution failed: {e}",
                            context={
                                'query': query[:100] + '...' if len(query) > 100 else query,
                                'batch_size': len(params_list)
                            }
                        )
    
    def _generate_query_hash(self, query: str, params: Optional[Tuple] = None) -> str:
        """Generate hash for query caching"""
        import hashlib
        
        query_string = query.strip().lower()
        if params:
            query_string += str(params)
        
        return hashlib.md5(query_string.encode()).hexdigest()
    
    def get_connection_stats(self) -> ConnectionStats:
        """Get connection pool statistics"""
        if not self.connection_pool:
            return ConnectionStats(0, 0, 0, 0, 0, 0.0, 0.0)
        
        # Calculate statistics
        total_queries = self.query_stats['total_queries']
        avg_query_time = (
            self.query_stats['total_time'] / total_queries 
            if total_queries > 0 else 0.0
        )
        
        cache_total = self.query_stats['cache_hits'] + self.query_stats['cache_misses']
        cache_hit_rate = (
            self.query_stats['cache_hits'] / cache_total 
            if cache_total > 0 else 0.0
        )
        
        return ConnectionStats(
            total_connections=self.config.pool_size,
            active_connections=0,  # Would need pool introspection
            idle_connections=0,    # Would need pool introspection
            waiting_connections=0, # Would need pool introspection
            total_queries=total_queries,
            avg_query_time=avg_query_time,
            cache_hit_rate=cache_hit_rate
        )
    
    def health_check(self) -> Dict[str, Any]:
        """Perform database health check"""
        try:
            # Test basic connectivity
            result = self.execute_query("SELECT 1 as health_check")
            
            if result.rows and result.rows[0]['health_check'] == 1:
                return {
                    'status': 'healthy',
                    'connection_pool': 'active',
                    'response_time_ms': result.execution_time * 1000,
                    'stats': self.get_connection_stats().__dict__
                }
            else:
                return {
                    'status': 'unhealthy',
                    'reason': 'Invalid health check response'
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'connection_pool': 'failed'
            }
    
    def optimize_query(self, query: str) -> Dict[str, Any]:
        """Analyze and suggest query optimizations"""
        try:
            # Get query execution plan
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            result = self.execute_query(explain_query)
            
            if result.rows:
                plan = result.rows[0]['QUERY PLAN'][0]
                
                # Analyze plan for optimization opportunities
                suggestions = []
                
                # Check for sequential scans
                if 'Seq Scan' in str(plan):
                    suggestions.append("Consider adding indexes for sequential scans")
                
                # Check for high cost operations
                if plan.get('Total Cost', 0) > 1000:
                    suggestions.append("Query has high cost - consider optimization")
                
                # Check for sorting operations
                if 'Sort' in str(plan):
                    suggestions.append("Consider adding indexes to avoid sorting")
                
                return {
                    'execution_plan': plan,
                    'execution_time': result.execution_time,
                    'suggestions': suggestions
                }
            
        except Exception as e:
            self.logger.error(f"Query optimization analysis failed: {e}")
            return {
                'error': str(e),
                'suggestions': ["Unable to analyze query"]
            }
    
    def close(self):
        """Close database connection pool"""
        if self.connection_pool:
            self.connection_pool.closeall()
            self.logger.info("Database connection pool closed")


# Async Database Manager (if asyncpg is available)
class AsyncDatabaseManager:
    """Async database manager for high-performance applications"""
    
    def __init__(self):
        if not ASYNCPG_AVAILABLE:
            raise SystemError("asyncpg not available. Install asyncpg package for async support.")
        
        self.config = config_manager.get_section('database')
        self.logger = get_logger(__name__)
        self.pool = None
    
    async def initialize(self):
        """Initialize async connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                host=self.config.host,
                port=self.config.port,
                database=self.config.database,
                user=self.config.username,
                password=self.config.password,
                min_size=1,
                max_size=self.config.pool_size,
                command_timeout=self.config.pool_timeout
            )
            
            self.logger.info("Async database pool initialized")
            
        except Exception as e:
            raise SystemError(f"Failed to initialize async database pool: {e}")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get async database connection"""
        async with self.pool.acquire() as connection:
            yield connection
    
    async def execute_query(self, query: str, *params) -> List[Dict[str, Any]]:
        """Execute async query"""
        async with self.get_connection() as conn:
            rows = await conn.fetch(query, *params)
            return [dict(row) for row in rows]
    
    async def close(self):
        """Close async connection pool"""
        if self.pool:
            await self.pool.close()


# Global database manager instance
database_manager = DatabaseManager()
