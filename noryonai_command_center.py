import os
import sys
import time
import json
import pickle
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
from colorama import init, Fore, Back, Style
import warnings
warnings.filterwarnings('ignore')

# Initialize colorama for colored terminal output
init(autoreset=True)

# Import our modules
from training.safe_training_system import SafeTrainingSystem
from training.real_data_loader import RealTradingDataLoader
# Simplified agent placeholders - the actual agents are in ai_core
# from ai_core.equity_agent import EquityAgent
# from ai_core.crypto_agent import CryptoAgent
# from ai_core.forex_agent import ForexAgent
# from ai_core.commodity_agent import CommodityAgent

class NoryonAICommandCenter:
    """Advanced AI Trading System Command Center"""
    
    def __init__(self):
        self.version = "2.0"
        self.models = {}
        self.agents = {}
        self.scaler = None
        self.symbol_encodings = {}
        self.is_running = True
        self.trading_active = False
        self.current_positions = {}
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
        
        # System status
        self.system_status = {
            'models_loaded': False,
            'agents_initialized': False,
            'data_streaming': False,
            'auto_trading': False
        }
        
        # Load configuration
        self.config = self.load_config()
        
    def load_config(self) -> Dict:
        """Load system configuration"""
        default_config = {
            'risk_per_trade': 0.02,  # 2% risk per trade
            'max_positions': 10,
            'confidence_threshold': 0.7,
            'data_update_interval': 5,  # seconds
            'auto_save_interval': 300,   # 5 minutes
            'training_data_path': 'data/paperswithbacktestStocks-1Min-Price',  # Default training data
            'model_save_path': 'models/command_center'  # Model save directory
        }
        
        if os.path.exists('config.json'):
            with open('config.json', 'r') as f:
                return json.load(f)
        return default_config
    
    def save_config(self):
        """Save current configuration"""
        with open('config.json', 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """Print system header"""
        self.clear_screen()
        print(Fore.CYAN + "=" * 80)
        print(Fore.CYAN + " " * 20 + "NORYONAI TRADING COMMAND CENTER v" + self.version)
        print(Fore.CYAN + " " * 25 + "AI-Powered Trading System")
        print(Fore.CYAN + "=" * 80)
        print()
    
    def print_status(self):
        """Print current system status"""
        print(Fore.YELLOW + "\n📊 SYSTEM STATUS:")
        print(Fore.WHITE + "─" * 40)
        
        # Model status
        status = "✅ LOADED" if self.system_status['models_loaded'] else "❌ NOT LOADED"
        color = Fore.GREEN if self.system_status['models_loaded'] else Fore.RED
        print(f"Models: {color}{status}")
        
        # Agent status
        status = "✅ ACTIVE" if self.system_status['agents_initialized'] else "❌ INACTIVE"
        color = Fore.GREEN if self.system_status['agents_initialized'] else Fore.RED
        print(f"Agents: {color}{status}")
        
        # Data streaming
        status = "✅ STREAMING" if self.system_status['data_streaming'] else "❌ OFFLINE"
        color = Fore.GREEN if self.system_status['data_streaming'] else Fore.RED
        print(f"Data Feed: {color}{status}")
        
        # Auto trading
        status = "✅ ENABLED" if self.system_status['auto_trading'] else "❌ DISABLED"
        color = Fore.GREEN if self.system_status['auto_trading'] else Fore.RED
        print(f"Auto Trading: {color}{status}")
        
        # Performance metrics
        print(Fore.YELLOW + "\n📈 PERFORMANCE:")
        print(Fore.WHITE + "─" * 40)
        print(f"Total Trades: {self.performance_metrics['total_trades']}")
        print(f"Win Rate: {self.performance_metrics['win_rate']:.1f}%")
        print(f"Total P&L: ${self.performance_metrics['total_pnl']:,.2f}")
        print(f"Active Positions: {len(self.current_positions)}")
    
    def print_menu(self):
        """Print main menu"""
        print(Fore.CYAN + "\n🎯 MAIN MENU:")
        print(Fore.WHITE + "─" * 40)
        print("1. 📚 Train Models")
        print("2. 🤖 Initialize Agents")
        print("3. 📊 Generate Trading Signals")
        print("4. 💹 Start Auto Trading")
        print("5. 📈 View Positions")
        print("6. 📉 Market Analysis")
        print("7. ⚙️  System Configuration")
        print("8. 💾 Save/Load Models")
        print("9. 📋 View Logs")
        print("0. 🚪 Exit")
        print(Fore.WHITE + "─" * 40)
    
    def train_models_menu(self):
        """Train models submenu"""
        self.print_header()
        print(Fore.YELLOW + "📚 MODEL TRAINING")
        print(Fore.WHITE + "─" * 40)
        print("1. Train on Real Data (500k samples)")
        print("2. Train on Sample Data (100k samples)")
        print("3. Quick Training (10k samples)")
        print("4. Back to Main Menu")
        
        choice = input(Fore.GREEN + "\nSelect option: ")
        
        if choice == '1':
            self.train_models(sample_size=500000)
        elif choice == '2':
            self.train_models(sample_size=100000)
        elif choice == '3':
            self.train_models(sample_size=10000)
    
    def train_models(self, sample_size: int = 100000):
        """Train all models"""
        print(Fore.YELLOW + f"\n🔄 Training models on {sample_size:,} samples...")
        
        try:
            # Load data
            loader = RealTradingDataLoader(self.config['training_data_path'])
            X, y = loader.load_and_prepare(sample_size=sample_size)
            
            # Split data
            from sklearn.model_selection import train_test_split
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Train models
            trainer = SafeTrainingSystem()
            self.models = trainer.train_ensemble(X_train, y_train)
            
            # Store scaler and encodings
            self.scaler = loader.scaler
            self.symbol_encodings = loader.symbol_encodings
            
            # Evaluate
            print(Fore.GREEN + "\n✅ Training complete! Evaluating models...")
            for name, model in self.models.items():
                accuracy = model.score(X_test, y_test)
                print(f"{name}: {accuracy:.4f} accuracy")
            
            self.system_status['models_loaded'] = True
            
            # Save models
            self.save_models()
            
        except Exception as e:
            print(Fore.RED + f"❌ Training failed: {e}")
        
        input(Fore.YELLOW + "\nPress Enter to continue...")
    
    def initialize_agents(self):
        """Initialize trading agents"""
        self.print_header()
        print(Fore.YELLOW + "🤖 INITIALIZING AGENTS...")
        
        try:
            # Create dummy agents for now (actual agents are in ai_core)
            class DummyAgent:
                def __init__(self, name):
                    self.name = name
                    self.models = None
                    self.scaler = None
                    
            self.agents = {
                'equity': DummyAgent('EquityAgent'),
                'crypto': DummyAgent('CryptoAgent'),
                'forex': DummyAgent('ForexAgent'),
                'commodity': DummyAgent('CommodityAgent')
            }
            
            # Set models for each agent if loaded
            if self.models:
                for agent in self.agents.values():
                    agent.models = self.models
                    agent.scaler = self.scaler
            
            self.system_status['agents_initialized'] = True
            print(Fore.GREEN + "✅ All agents initialized successfully!")
            print(Fore.CYAN + "   - EquityAgent (Stocks)")
            print(Fore.CYAN + "   - CryptoAgent (Cryptocurrency)")
            print(Fore.CYAN + "   - ForexAgent (Currency Pairs)")
            print(Fore.CYAN + "   - CommodityAgent (Commodities)")
            
        except Exception as e:
            print(Fore.RED + f"❌ Agent initialization failed: {e}")
        
        input(Fore.YELLOW + "\nPress Enter to continue...")
    
    def generate_signals(self):
        """Generate trading signals"""
        self.print_header()
        print(Fore.YELLOW + "📊 GENERATING TRADING SIGNALS...")
        
        if not self.system_status['models_loaded']:
            print(Fore.RED + "❌ Models not loaded! Please train models first.")
            input(Fore.YELLOW + "\nPress Enter to continue...")
            return
        
        # Simulate getting recent market data
        print(Fore.CYAN + "\n🔍 Analyzing markets...")
        
        # Sample market data (in production, this would come from real-time feed)
        markets = {
            'AAPL': {'price': 175.50, 'volume': 1250000, 'change': 0.015},
            'BTC': {'price': 43250.00, 'volume': 850000, 'change': 0.023},
            'EUR/USD': {'price': 1.0875, 'volume': 2150000, 'change': -0.002},
            'GOLD': {'price': 2035.50, 'volume': 450000, 'change': 0.008}
        }
        
        signals = []
        
        for symbol, data in markets.items():
            # Create sample features (in production, calculate from real data)
            sample_features = self._create_sample_features(data)
            
            # Get prediction
            if hasattr(self, 'models') and self.models:
                prediction = self._predict_with_ensemble(sample_features)
                
                if prediction['confidence'] > self.config['confidence_threshold']:
                    signals.append({
                        'symbol': symbol,
                        'signal': prediction['signal'],
                        'confidence': prediction['confidence'],
                        'price': data['price']
                    })
        
        # Display signals
        print(Fore.GREEN + f"\n✅ Found {len(signals)} high-confidence signals:")
        print(Fore.WHITE + "─" * 60)
        print(f"{'Symbol':<10} {'Signal':<10} {'Confidence':<12} {'Price':<10}")
        print(Fore.WHITE + "─" * 60)
        
        for signal in signals:
            color = Fore.GREEN if signal['signal'] == 'BUY' else Fore.RED
            print(f"{signal['symbol']:<10} {color}{signal['signal']:<10} "
                  f"{Fore.YELLOW}{signal['confidence']:.1%}  "
                  f"{Fore.WHITE}${signal['price']:,.2f}")
        
        input(Fore.YELLOW + "\nPress Enter to continue...")
    
    def _create_sample_features(self, market_data: Dict) -> np.ndarray:
        """Create sample features from market data"""
        # This is a simplified version - in production, calculate real technical indicators
        features = {
            'returns': market_data['change'],
            'log_returns': np.log(1 + market_data['change']),
            'high_low_ratio': 1.01,
            'close_open_ratio': 1 + market_data['change'],
            'volume_ratio': 1.1,
            'price_to_sma_5': 1.005,
            'price_to_sma_10': 1.003,
            'price_to_sma_20': 1.001,
            'price_to_sma_50': 0.998,
            'macd': 0.1,
            'macd_signal': 0.08,
            'macd_histogram': 0.02,
            'rsi': 55 + (market_data['change'] * 100),
            'bb_position': 0.5 + market_data['change'],
            'atr': 2.0,
            'price_position': 0.6,
            'hour': datetime.now().hour,
            'day_of_week': datetime.now().weekday(),
            'is_market_open': 1 if 9 <= datetime.now().hour <= 16 else 0,
            'symbol_encoded': 0
        }
        
        feature_array = np.array([list(features.values())])
        
        if self.scaler:
            feature_array = self.scaler.transform(feature_array)
        
        return feature_array
    
    def _predict_with_ensemble(self, features: np.ndarray) -> Dict:
        """Make prediction using ensemble of models"""
        predictions = {}
        
        for name, model in self.models.items():
            pred = model.predict(features)[0]
            prob = model.predict_proba(features)[0]
            predictions[name] = {
                'prediction': 'BUY' if pred == 1 else 'SELL',
                'confidence': max(prob)
            }
        
        # Ensemble decision
        buy_votes = sum(1 for p in predictions.values() if p['prediction'] == 'BUY')
        sell_votes = len(predictions) - buy_votes
        
        if buy_votes > sell_votes:
            signal = 'BUY'
            confidence = np.mean([p['confidence'] for p in predictions.values() 
                                if p['prediction'] == 'BUY'])
        elif sell_votes > buy_votes:
            signal = 'SELL'
            confidence = np.mean([p['confidence'] for p in predictions.values() 
                                if p['prediction'] == 'SELL'])
        else:
            signal = 'HOLD'
            confidence = 0.5
        
        return {
            'signal': signal,
            'confidence': confidence,
            'votes': {'buy': buy_votes, 'sell': sell_votes}
        }
    
    def view_positions(self):
        """View current positions"""
        self.print_header()
        print(Fore.YELLOW + "📈 CURRENT POSITIONS")
        print(Fore.WHITE + "─" * 80)
        
        if not self.current_positions:
            print(Fore.CYAN + "No active positions")
        else:
            print(f"{'Symbol':<10} {'Type':<10} {'Entry':<10} {'Current':<10} "
                  f"{'P&L':<12} {'Time'}")
            print(Fore.WHITE + "─" * 80)
            
            for symbol, pos in self.current_positions.items():
                pnl = (pos['current_price'] - pos['entry_price']) * pos['quantity']
                if pos['type'] == 'SHORT':
                    pnl = -pnl
                
                color = Fore.GREEN if pnl >= 0 else Fore.RED
                print(f"{symbol:<10} {pos['type']:<10} ${pos['entry_price']:<9.2f} "
                      f"${pos['current_price']:<9.2f} {color}${pnl:<11.2f} "
                      f"{Fore.WHITE}{pos['entry_time']}")
        
        input(Fore.YELLOW + "\nPress Enter to continue...")
    
    def save_models(self):
        """Save trained models"""
        print(Fore.YELLOW + "\n💾 Saving models...")
        
        os.makedirs(self.config['model_save_path'], exist_ok=True)
        
        # Save models
        for name, model in self.models.items():
            path = f"{self.config['model_save_path']}/{name}.pkl"
            with open(path, 'wb') as f:
                pickle.dump(model, f)
        
        # Save scaler
        if self.scaler:
            with open(f"{self.config['model_save_path']}/scaler.pkl", 'wb') as f:
                pickle.dump(self.scaler, f)
        
        # Save encodings
        with open(f"{self.config['model_save_path']}/encodings.json", 'w') as f:
            json.dump(self.symbol_encodings, f)
        
        print(Fore.GREEN + "✅ Models saved successfully!")
    
    def load_models(self):
        """Load saved models"""
        print(Fore.YELLOW + "\n💾 Loading models...")
        
        try:
            model_names = ['RandomForest', 'XGBoost', 'LightGBM', 'LogisticRegression']
            self.models = {}
            
            for name in model_names:
                path = f"{self.config['model_save_path']}/{name}.pkl"
                if os.path.exists(path):
                    with open(path, 'rb') as f:
                        self.models[name] = pickle.load(f)
            
            # Load scaler
            scaler_path = f"{self.config['model_save_path']}/scaler.pkl"
            if os.path.exists(scaler_path):
                with open(scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
            
            # Load encodings
            encoding_path = f"{self.config['model_save_path']}/encodings.json"
            if os.path.exists(encoding_path):
                with open(encoding_path, 'r') as f:
                    self.symbol_encodings = json.load(f)
            
            if self.models:
                self.system_status['models_loaded'] = True
                print(Fore.GREEN + f"✅ Loaded {len(self.models)} models successfully!")
            else:
                print(Fore.RED + "❌ No saved models found")
                
        except Exception as e:
            print(Fore.RED + f"❌ Error loading models: {e}")
    
    def system_config_menu(self):
        """System configuration menu"""
        self.print_header()
        print(Fore.YELLOW + "⚙️  SYSTEM CONFIGURATION")
        print(Fore.WHITE + "─" * 40)
        print(f"1. Risk per trade: {self.config['risk_per_trade']*100:.1f}%")
        print(f"2. Max positions: {self.config['max_positions']}")
        print(f"3. Confidence threshold: {self.config['confidence_threshold']*100:.0f}%")
        print(f"4. Data update interval: {self.config['data_update_interval']}s")
        print(f"5. Save configuration")
        print(f"6. Back to main menu")
        
        choice = input(Fore.GREEN + "\nSelect option to modify: ")
        
        if choice == '1':
            value = float(input("New risk per trade (0.01-0.05): "))
            self.config['risk_per_trade'] = max(0.01, min(0.05, value))
        elif choice == '2':
            value = int(input("New max positions (1-20): "))
            self.config['max_positions'] = max(1, min(20, value))
        elif choice == '3':
            value = float(input("New confidence threshold (0.5-0.9): "))
            self.config['confidence_threshold'] = max(0.5, min(0.9, value))
        elif choice == '4':
            value = int(input("New update interval in seconds (1-60): "))
            self.config['data_update_interval'] = max(1, min(60, value))
        elif choice == '5':
            self.save_config()
            print(Fore.GREEN + "✅ Configuration saved!")
            time.sleep(1)
    
    def run(self):
        """Main command center loop"""
        # Try to load saved models on startup
        self.load_models()
        
        while self.is_running:
            self.print_header()
            self.print_status()
            self.print_menu()
            
            choice = input(Fore.GREEN + "\nSelect option: ")
            
            if choice == '1':
                self.train_models_menu()
            elif choice == '2':
                self.initialize_agents()
            elif choice == '3':
                self.generate_signals()
            elif choice == '4':
                print(Fore.YELLOW + "🚧 Auto trading coming soon...")
                input(Fore.YELLOW + "\nPress Enter to continue...")
            elif choice == '5':
                self.view_positions()
            elif choice == '6':
                print(Fore.YELLOW + "🚧 Market analysis coming soon...")
                input(Fore.YELLOW + "\nPress Enter to continue...")
            elif choice == '7':
                self.system_config_menu()
            elif choice == '8':
                if self.models:
                    self.save_models()
                else:
                    self.load_models()
                input(Fore.YELLOW + "\nPress Enter to continue...")
            elif choice == '9':
                print(Fore.YELLOW + "🚧 Log viewer coming soon...")
                input(Fore.YELLOW + "\nPress Enter to continue...")
            elif choice == '0':
                self.shutdown()
            else:
                print(Fore.RED + "Invalid option!")
                time.sleep(1)
    
    def shutdown(self):
        """Shutdown the system"""
        print(Fore.YELLOW + "\n🔄 Shutting down...")
        
        # Save current state
        if self.models:
            self.save_models()
        
        self.save_config()
        
        # Stop any running threads
        self.is_running = False
        self.trading_active = False
        
        print(Fore.GREEN + "✅ System shutdown complete. Goodbye!")
        time.sleep(1)


def main():
    """Main entry point"""
    try:
        # Create and run command center
        command_center = NoryonAICommandCenter()
        command_center.run()
    except KeyboardInterrupt:
        print(Fore.YELLOW + "\n\n⚠️  Keyboard interrupt detected. Shutting down...")
    except Exception as e:
        print(Fore.RED + f"\n\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print(Fore.CYAN + "\nThank you for using NoryonAI Trading System!")


if __name__ == "__main__":
    main() 