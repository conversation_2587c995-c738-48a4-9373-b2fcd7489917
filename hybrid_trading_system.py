#!/usr/bin/env python
"""
Hybrid Trading System - Local + API LLMs
Optimized for hardware limits
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import asyncio
from typing import Dict, List
import time
import os

# API imports (only when needed)
try:
    import openai
    import anthropic
    API_AVAILABLE = True
except:
    API_AVAILABLE = False

class HybridTradingBrain:
    """Smart hybrid system - uses local first, API when needed"""
    
    def __init__(self):
        # Local model (FREE)
        self.qwen_path = "qwen ai"
        self.qwen_model = None
        self.qwen_tokenizer = None
        
        # API keys (only if available)
        self.api_keys = {
            'openai': os.getenv('OPENAI_API_KEY'),
            'anthropic': os.getenv('ANTHROPIC_API_KEY')
        }
        
        # Decision thresholds
        self.complexity_threshold = 0.7
        self.api_call_count = 0
        self.api_budget_daily = 5.0  # $5/day max
        
    def load_local_model(self):
        """Load Qwen 14B once"""
        if self.qwen_model is not None:
            return
            
        print("Loading Qwen 14B (FREE local model)...")
        
        self.qwen_tokenizer = AutoTokenizer.from_pretrained(
            self.qwen_path,
            trust_remote_code=True,
            use_fast=False
        )
        
        # Optimize for your hardware
        self.qwen_model = AutoModelForCausalLM.from_pretrained(
            self.qwen_path,
            torch_dtype=torch.float32,
            device_map="cpu",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            load_in_8bit=False  # Disable for CPU
        )
        
        print("✅ Local model ready!")
        
    async def analyze_market(self, market_data: Dict) -> Dict:
        """Smart routing: Local first, API only if needed"""
        
        # Step 1: Assess complexity
        complexity = self.assess_complexity(market_data)
        
        # Step 2: Route to appropriate model
        if complexity < self.complexity_threshold:
            # Use local Qwen (FREE)
            return await self.local_analysis(market_data)
        else:
            # Complex scenario - use API if budget allows
            if self.api_call_count * 0.10 < self.api_budget_daily:
                return await self.api_analysis(market_data)
            else:
                # Budget exceeded - use local with warning
                result = await self.local_analysis(market_data)
                result['warning'] = "Complex scenario but API budget exceeded"
                return result
                
    def assess_complexity(self, market_data: Dict) -> float:
        """Determine if this needs API analysis"""
        
        complexity_score = 0.0
        
        # High volatility
        if market_data.get('volatility', 0) > 30:
            complexity_score += 0.3
            
        # Multiple conflicting signals
        if market_data.get('conflicting_signals', False):
            complexity_score += 0.3
            
        # News events
        if market_data.get('news_impact', 'none') != 'none':
            complexity_score += 0.2
            
        # Unusual patterns
        if market_data.get('pattern', '') in ['black_swan', 'flash_crash', 'unusual']:
            complexity_score += 0.4
            
        return complexity_score
        
    async def local_analysis(self, market_data: Dict) -> Dict:
        """Fast local analysis with Qwen"""
        
        if self.qwen_model is None:
            self.load_local_model()
            
        prompt = f"""Analyze this forex data:
Pair: {market_data.get('pair')}
Price: {market_data.get('price')}
RSI: {market_data.get('rsi')}
Trend: {market_data.get('trend')}

Decision: BUY/SELL/HOLD with confidence %"""

        # Generate with Qwen
        inputs = self.qwen_tokenizer(prompt, return_tensors="pt")
        
        start = time.time()
        with torch.no_grad():
            outputs = self.qwen_model.generate(
                **inputs,
                max_new_tokens=100,
                temperature=0.7,
                pad_token_id=self.qwen_tokenizer.eos_token_id
            )
            
        response = self.qwen_tokenizer.decode(outputs[0], skip_special_tokens=True)
        elapsed = time.time() - start
        
        return {
            'source': 'Qwen-14B-Local',
            'decision': self.parse_decision(response),
            'confidence': 0.75,
            'reasoning': response,
            'time': elapsed,
            'cost': 0.0
        }
        
    async def api_analysis(self, market_data: Dict) -> Dict:
        """Complex analysis with API models"""
        
        if not API_AVAILABLE or not self.api_keys['openai']:
            return await self.local_analysis(market_data)
            
        # Use GPT-4 for complex scenarios
        prompt = f"""You are an expert trader. Analyze this complex scenario:
        
Market: {market_data}

Provide:
1. Decision (BUY/SELL/HOLD)
2. Risk assessment
3. Key factors
4. Suggested parameters"""

        try:
            # Simulated API call (replace with actual)
            self.api_call_count += 1
            
            return {
                'source': 'GPT-4-API',
                'decision': 'hold',  # Would be actual API response
                'confidence': 0.85,
                'reasoning': 'Complex market conditions detected...',
                'time': 1.5,
                'cost': 0.10
            }
        except:
            # Fallback to local
            return await self.local_analysis(market_data)
            
    def parse_decision(self, response: str) -> str:
        """Extract decision from response"""
        response_upper = response.upper()
        
        if 'BUY' in response_upper:
            return 'buy'
        elif 'SELL' in response_upper:
            return 'sell'
        else:
            return 'hold'

class SmartNORYONAI:
    """Your complete hybrid trading system"""
    
    def __init__(self):
        # Your 74 models
        self.ai_models = self.load_74_models()
        
        # Hybrid brain
        self.hybrid_brain = HybridTradingBrain()
        
        # Performance tracking
        self.daily_stats = {
            'decisions': 0,
            'api_calls': 0,
            'total_cost': 0.0,
            'win_rate': 0.0
        }
        
    def load_74_models(self):
        """Your existing 74 models"""
        return {'status': 'ready', 'count': 74}
        
    async def make_decision(self, market_data: Dict) -> Dict:
        """Smart decision routing"""
        
        print(f"\n🔍 Analyzing {market_data.get('pair')}...")
        
        # Get signals from your 74 models
        ai_signals = self.get_ai_signals(market_data)
        
        # Determine if we need LLM analysis
        if ai_signals['confidence'] > 0.9:
            # Very clear signal - no LLM needed
            print("📊 Clear signal from 74 models - no LLM needed")
            return {
                'action': ai_signals['consensus'],
                'confidence': ai_signals['confidence'],
                'source': '74-Models-Only',
                'cost': 0.0
            }
        else:
            # Need LLM validation
            llm_result = await self.hybrid_brain.analyze_market(market_data)
            
            print(f"🧠 LLM Analysis: {llm_result['source']}")
            print(f"💰 Cost: ${llm_result['cost']:.2f}")
            
            # Combine decisions
            return self.combine_all_signals(ai_signals, llm_result)
            
    def get_ai_signals(self, market_data: Dict) -> Dict:
        """Get consensus from 74 models"""
        
        # Simulate your models
        # In reality, this calls your actual models
        
        buy_votes = 45
        sell_votes = 20
        hold_votes = 9
        
        total = buy_votes + sell_votes + hold_votes
        
        if buy_votes > sell_votes and buy_votes > hold_votes:
            consensus = 'buy'
            confidence = buy_votes / total
        elif sell_votes > buy_votes:
            consensus = 'sell'
            confidence = sell_votes / total
        else:
            consensus = 'hold'
            confidence = hold_votes / total
            
        return {
            'consensus': consensus,
            'confidence': confidence,
            'distribution': f"{buy_votes}B/{sell_votes}S/{hold_votes}H"
        }
        
    def combine_all_signals(self, ai_signals: Dict, llm_result: Dict) -> Dict:
        """Smart combination of all signals"""
        
        # If they agree - high confidence
        if ai_signals['consensus'] == llm_result['decision']:
            final_confidence = (ai_signals['confidence'] + llm_result['confidence']) / 2
            final_decision = ai_signals['consensus']
        else:
            # Disagreement - be cautious
            final_confidence = 0.5
            final_decision = 'hold'
            
        self.daily_stats['decisions'] += 1
        self.daily_stats['total_cost'] += llm_result['cost']
        
        return {
            'action': final_decision,
            'confidence': final_confidence,
            'ai_models': ai_signals['distribution'],
            'llm': llm_result['source'],
            'cost': llm_result['cost'],
            'daily_cost': self.daily_stats['total_cost']
        }

# Demo
async def demo_hybrid_system():
    """Show the hybrid system in action"""
    
    print("=" * 60)
    print("🚀 HYBRID NORYONAI TRADING SYSTEM")
    print("=" * 60)
    print("\n📊 Configuration:")
    print("- 74 AI Models: Always active")
    print("- Qwen 14B: Local decisions (FREE)")
    print("- GPT-4/Claude: Complex scenarios ($0.10/call)")
    print("- Daily budget: $5.00")
    
    system = SmartNORYONAI()
    
    # Test scenarios
    scenarios = [
        {
            'name': 'Simple trend',
            'pair': 'EURUSD',
            'price': 1.0950,
            'rsi': 65,
            'trend': 'bullish',
            'volatility': 15
        },
        {
            'name': 'Complex scenario',
            'pair': 'GBPUSD',
            'price': 1.2850,
            'rsi': 50,
            'trend': 'mixed',
            'volatility': 45,
            'conflicting_signals': True,
            'news_impact': 'high'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n\n🎯 Testing: {scenario['name']}")
        print("-" * 40)
        
        decision = await system.make_decision(scenario)
        
        print(f"\n📋 Final Decision:")
        print(f"Action: {decision['action'].upper()}")
        print(f"Confidence: {decision['confidence']:.1%}")
        print(f"Source: {decision['llm']}")
        print(f"Cost: ${decision['cost']:.2f}")
        print(f"Daily total: ${decision['daily_cost']:.2f}")
    
    print("\n\n✅ Hybrid system ready!")
    print(f"Expected daily cost: $2-5 (vs $50-100 for API-only)")

if __name__ == "__main__":
    asyncio.run(demo_hybrid_system()) 