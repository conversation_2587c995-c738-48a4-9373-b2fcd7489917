#!/usr/bin/env python3
"""
Create Test Models for AI Model Testing
Generates synthetic trained models for testing the model testing framework.
"""

import numpy as np
import pandas as pd
import joblib
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, r2_score
import warnings
warnings.filterwarnings('ignore')

def create_directories():
    """Create model directory structure"""
    directories = [
        'models/financial',
        'models/reasoning', 
        'models/classification',
        'models/regression',
        'models/other'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Model directories created")

def generate_financial_data(n_samples=5000):
    """Generate synthetic financial market data"""
    np.random.seed(42)
    
    # Generate realistic financial features
    data = {
        'open': np.random.uniform(1.0, 2.0, n_samples),
        'high': np.random.uniform(1.0, 2.0, n_samples),
        'low': np.random.uniform(1.0, 2.0, n_samples),
        'close': np.random.uniform(1.0, 2.0, n_samples),
        'volume': np.random.uniform(1000, 100000, n_samples),
        'rsi': np.random.uniform(0, 100, n_samples),
        'macd': np.random.uniform(-0.1, 0.1, n_samples),
        'sma_20': np.random.uniform(1.0, 2.0, n_samples),
        'sma_50': np.random.uniform(1.0, 2.0, n_samples),
        'volatility': np.random.uniform(0.01, 0.05, n_samples),
        'returns': np.random.normal(0, 0.02, n_samples),
        'bollinger_upper': np.random.uniform(1.0, 2.0, n_samples)
    }
    
    df = pd.DataFrame(data)
    
    # Create realistic target (buy/sell signals)
    # Buy signal when RSI < 30 and MACD > 0, or when close > sma_20
    buy_conditions = (
        ((df['rsi'] < 30) & (df['macd'] > 0)) |
        (df['close'] > df['sma_20']) |
        (df['returns'] > 0.01)
    )
    
    df['target'] = buy_conditions.astype(int)
    
    return df

def generate_reasoning_data(n_samples=3000):
    """Generate synthetic reasoning/logic data"""
    np.random.seed(123)
    
    # Create logical reasoning features
    data = {
        'premise_1': np.random.randint(0, 2, n_samples),  # Boolean premises
        'premise_2': np.random.randint(0, 2, n_samples),
        'premise_3': np.random.randint(0, 2, n_samples),
        'logical_operator': np.random.randint(0, 3, n_samples),  # AND, OR, NOT
        'context_strength': np.random.uniform(0, 1, n_samples),
        'evidence_weight': np.random.uniform(0, 1, n_samples),
        'prior_probability': np.random.uniform(0, 1, n_samples),
        'complexity_score': np.random.uniform(0, 10, n_samples),
        'confidence_level': np.random.uniform(0.5, 1.0, n_samples),
        'reasoning_depth': np.random.randint(1, 5, n_samples)
    }
    
    df = pd.DataFrame(data)
    
    # Create logical target based on premises and operators
    target = np.zeros(n_samples)
    for i in range(n_samples):
        p1, p2, p3 = df.iloc[i]['premise_1'], df.iloc[i]['premise_2'], df.iloc[i]['premise_3']
        op = df.iloc[i]['logical_operator']
        
        if op == 0:  # AND
            target[i] = int(p1 and p2 and p3)
        elif op == 1:  # OR
            target[i] = int(p1 or p2 or p3)
        else:  # NOT (of first premise)
            target[i] = int(not p1)
    
    df['target'] = target
    
    return df

def generate_classification_data(n_samples=4000):
    """Generate synthetic classification data"""
    np.random.seed(456)
    
    # Multi-class classification problem
    n_features = 15
    X = np.random.randn(n_samples, n_features)
    
    # Create non-linear decision boundaries
    y = np.zeros(n_samples)
    for i in range(n_samples):
        if X[i, 0] + X[i, 1] > 1:
            y[i] = 2
        elif X[i, 2] * X[i, 3] > 0:
            y[i] = 1
        else:
            y[i] = 0
    
    # Add some noise
    noise_indices = np.random.choice(n_samples, size=int(0.1 * n_samples), replace=False)
    y[noise_indices] = np.random.randint(0, 3, len(noise_indices))
    
    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)])
    df['target'] = y.astype(int)
    
    return df

def generate_regression_data(n_samples=3500):
    """Generate synthetic regression data"""
    np.random.seed(789)
    
    # Multi-dimensional regression problem
    n_features = 12
    X = np.random.randn(n_samples, n_features)
    
    # Create complex non-linear target
    y = (
        2 * X[:, 0] + 
        1.5 * X[:, 1] * X[:, 2] + 
        0.5 * X[:, 3] ** 2 + 
        np.sin(X[:, 4]) + 
        np.random.randn(n_samples) * 0.2
    )
    
    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)])
    df['target'] = y
    
    return df

def create_financial_models():
    """Create and train financial trading models"""
    print("🏦 Creating Financial Models...")
    
    # Generate financial data
    data = generate_financial_data()
    X = data.drop('target', axis=1)
    y = data['target']
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    models = {
        'eurusd_classifier_rf': RandomForestClassifier(n_estimators=100, random_state=42),
        'eurusd_classifier_lr': LogisticRegression(random_state=42, max_iter=1000),
        'gbpusd_classifier_rf': RandomForestClassifier(n_estimators=150, random_state=123),
        'usdjpy_classifier_svm': SVC(probability=True, random_state=42),
        'forex_ensemble_model': RandomForestClassifier(n_estimators=200, random_state=456)
    }
    
    for name, model in models.items():
        print(f"  Training {name}...")
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"    Accuracy: {accuracy:.3f}")
        
        # Save model
        model_path = f"models/financial/{name}.pkl"
        joblib.dump(model, model_path)
        print(f"    Saved: {model_path}")
    
    print(f"✅ Created {len(models)} financial models")

def create_reasoning_models():
    """Create and train reasoning/logic models"""
    print("🧠 Creating Reasoning Models...")
    
    # Generate reasoning data
    data = generate_reasoning_data()
    X = data.drop('target', axis=1)
    y = data['target']
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    models = {
        'logical_reasoning_rf': RandomForestClassifier(n_estimators=100, random_state=42),
        'premise_evaluator': LogisticRegression(random_state=42, max_iter=1000),
        'inference_engine': RandomForestClassifier(n_estimators=80, random_state=123),
        'decision_tree_logic': RandomForestClassifier(n_estimators=50, random_state=456)
    }
    
    for name, model in models.items():
        print(f"  Training {name}...")
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"    Accuracy: {accuracy:.3f}")
        
        # Save model
        model_path = f"models/reasoning/{name}.pkl"
        joblib.dump(model, model_path)
        print(f"    Saved: {model_path}")
    
    print(f"✅ Created {len(models)} reasoning models")

def create_classification_models():
    """Create and train general classification models"""
    print("📊 Creating Classification Models...")
    
    # Generate classification data
    data = generate_classification_data()
    X = data.drop('target', axis=1)
    y = data['target']
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    models = {
        'multiclass_classifier_rf': RandomForestClassifier(n_estimators=100, random_state=42),
        'pattern_recognition_lr': LogisticRegression(random_state=42, max_iter=1000),
        'feature_classifier_svm': SVC(probability=True, random_state=42),
        'ensemble_classifier': RandomForestClassifier(n_estimators=120, random_state=123)
    }
    
    for name, model in models.items():
        print(f"  Training {name}...")
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"    Accuracy: {accuracy:.3f}")
        
        # Save model
        model_path = f"models/classification/{name}.pkl"
        joblib.dump(model, model_path)
        print(f"    Saved: {model_path}")
    
    print(f"✅ Created {len(models)} classification models")

def create_regression_models():
    """Create and train regression models"""
    print("📈 Creating Regression Models...")
    
    # Generate regression data
    data = generate_regression_data()
    X = data.drop('target', axis=1)
    y = data['target']
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    models = {
        'price_predictor_rf': RandomForestRegressor(n_estimators=100, random_state=42),
        'trend_predictor_lr': LinearRegression(),
        'volatility_predictor_svr': SVR(kernel='rbf'),
        'ensemble_regressor': RandomForestRegressor(n_estimators=150, random_state=123)
    }
    
    for name, model in models.items():
        print(f"  Training {name}...")
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        r2 = r2_score(y_test, y_pred)
        print(f"    R² Score: {r2:.3f}")
        
        # Save model
        model_path = f"models/regression/{name}.pkl"
        joblib.dump(model, model_path)
        print(f"    Saved: {model_path}")
    
    print(f"✅ Created {len(models)} regression models")

def create_other_models():
    """Create miscellaneous models"""
    print("🔧 Creating Other Models...")
    
    # Create some additional models for testing edge cases
    data = generate_classification_data(1000)
    X = data.drop('target', axis=1)
    y = data['target']
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    models = {
        'experimental_model': RandomForestClassifier(n_estimators=50, random_state=42),
        'prototype_classifier': LogisticRegression(random_state=42, max_iter=500)
    }
    
    for name, model in models.items():
        print(f"  Training {name}...")
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"    Accuracy: {accuracy:.3f}")
        
        # Save model
        model_path = f"models/other/{name}.pkl"
        joblib.dump(model, model_path)
        print(f"    Saved: {model_path}")
    
    print(f"✅ Created {len(models)} other models")

def main():
    """Create all test models"""
    print("🚀 CREATING TEST MODELS FOR AI TESTING")
    print("=" * 50)
    
    # Create directory structure
    create_directories()
    
    # Create models by category
    create_financial_models()
    create_reasoning_models()
    create_classification_models()
    create_regression_models()
    create_other_models()
    
    # Summary
    total_models = 0
    for category in ['financial', 'reasoning', 'classification', 'regression', 'other']:
        model_dir = Path(f'models/{category}')
        if model_dir.exists():
            model_count = len(list(model_dir.glob('*.pkl')))
            total_models += model_count
            print(f"📁 {category}: {model_count} models")
    
    print("=" * 50)
    print(f"🎉 SUCCESSFULLY CREATED {total_models} TEST MODELS!")
    print("✅ Ready for comprehensive AI model testing")

if __name__ == "__main__":
    main()
