# Prometheus Alert Rules for NoryonAI

groups:
  # Application Health Alerts
  - name: noryonai.application
    rules:
      - alert: NoryonAIBackendDown
        expr: up{job="noryonai-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: noryonai-backend
        annotations:
          summary: "NoryonAI Backend is down"
          description: "NoryonAI Backend instance {{ $labels.instance }} has been down for more than 1 minute."
          runbook_url: "https://docs.noryonai.com/runbooks/backend-down"

      - alert: NoryonAIHighErrorRate
        expr: rate(noryonai_http_requests_total{status=~"5.."}[5m]) / rate(noryonai_http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: noryonai-backend
        annotations:
          summary: "High error rate in NoryonAI Backend"
          description: "Error rate is {{ $value | humanizePercentage }} for instance {{ $labels.instance }}"

      - alert: NoryonAIHighLatency
        expr: histogram_quantile(0.95, rate(noryonai_http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: noryonai-backend
        annotations:
          summary: "High latency in NoryonAI Backend"
          description: "95th percentile latency is {{ $value }}s for instance {{ $labels.instance }}"

      - alert: NoryonAIModelPredictionFailures
        expr: rate(noryonai_model_prediction_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: noryonai-backend
        annotations:
          summary: "High model prediction failure rate"
          description: "Model prediction error rate is {{ $value }} per second"

  # Resource Usage Alerts
  - name: noryonai.resources
    rules:
      - alert: NoryonAIHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{pod=~"noryonai-backend-.*"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          service: noryonai-backend
        annotations:
          summary: "High CPU usage in NoryonAI Backend"
          description: "CPU usage is {{ $value }}% for pod {{ $labels.pod }}"

      - alert: NoryonAIHighMemoryUsage
        expr: container_memory_usage_bytes{pod=~"noryonai-backend-.*"} / container_spec_memory_limit_bytes * 100 > 85
        for: 10m
        labels:
          severity: warning
          service: noryonai-backend
        annotations:
          summary: "High memory usage in NoryonAI Backend"
          description: "Memory usage is {{ $value }}% for pod {{ $labels.pod }}"

      - alert: NoryonAIModelCacheEvictions
        expr: rate(noryonai_model_cache_evictions_total[5m]) > 1
        for: 5m
        labels:
          severity: info
          service: noryonai-backend
        annotations:
          summary: "High model cache eviction rate"
          description: "Model cache eviction rate is {{ $value }} per second"

  # Database Alerts
  - name: noryonai.database
    rules:
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL instance {{ $labels.instance }} has been down for more than 1 minute."

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "High PostgreSQL connection usage"
          description: "PostgreSQL connection usage is {{ $value }}%"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "Query efficiency is low: {{ $value }}"

  # Cache Alerts
  - name: noryonai.cache
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis instance {{ $labels.instance }} has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis memory usage"
          description: "Redis memory usage is {{ $value }}%"

      - alert: RedisHighConnectionCount
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis connection count"
          description: "Redis has {{ $value }} connected clients"

  # Infrastructure Alerts
  - name: noryonai.infrastructure
    rules:
      - alert: KubernetesNodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
          service: kubernetes
        annotations:
          summary: "Kubernetes node not ready"
          description: "Node {{ $labels.node }} has been not ready for more than 5 minutes."

      - alert: KubernetesPodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is crash looping."

      - alert: KubernetesDeploymentReplicasMismatch
        expr: kube_deployment_spec_replicas != kube_deployment_status_available_replicas
        for: 10m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "Deployment replicas mismatch"
          description: "Deployment {{ $labels.namespace }}/{{ $labels.deployment }} has {{ $value }} available replicas, expected {{ $labels.spec_replicas }}."

  # Business Logic Alerts
  - name: noryonai.business
    rules:
      - alert: NoryonAITradingSystemDown
        expr: noryonai_trading_system_status == 0
        for: 2m
        labels:
          severity: critical
          service: trading
        annotations:
          summary: "Trading system is down"
          description: "NoryonAI trading system has been down for more than 2 minutes."

      - alert: NoryonAILowModelAccuracy
        expr: noryonai_model_accuracy < 0.7
        for: 15m
        labels:
          severity: warning
          service: models
        annotations:
          summary: "Model accuracy below threshold"
          description: "Model {{ $labels.model_name }} accuracy is {{ $value }}, below 70% threshold."

      - alert: NoryonAIHighTradingLosses
        expr: rate(noryonai_trading_losses_total[1h]) > 1000
        for: 5m
        labels:
          severity: critical
          service: trading
        annotations:
          summary: "High trading losses detected"
          description: "Trading losses rate is {{ $value }} per hour, exceeding threshold."

      - alert: NoryonAIDataProcessingBacklog
        expr: noryonai_data_processing_queue_size > 1000
        for: 10m
        labels:
          severity: warning
          service: data-processing
        annotations:
          summary: "Data processing backlog"
          description: "Data processing queue has {{ $value }} items, indicating a backlog."
